import { NextRequest, NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';
import { APP_CONFIG } from '@/lib/config';
import { getQdrantCollectionName } from '@/lib/qdrant-utils';

// Simple API key authentication for n8n
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-api-key') || request.headers.get('authorization')?.replace('Bearer ', '');
  
  // For now, we'll use a simple API key check
  // In production, you should use a proper API key management system
  const validApiKey = process.env.N8N_API_KEY || 'your-secret-api-key';
  
  return apiKey === validApiKey;
}

export async function POST(request: NextRequest) {
  try {
    // Validate API key for n8n access
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Unauthorized. Please provide a valid API key in the x-api-key header or Authorization header.' 
        },
        { status: 401 }
      );
    }

    // Check if Qdrant is configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Qdrant is not configured. Vector search is not available.',
          code: 'QDRANT_NOT_CONFIGURED'
        },
        { status: 503 }
      );
    }

    // Initialize Qdrant client
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    const body = await request.json();
    const { 
      query, 
      collection_id, 
      user_id, 
      vector, 
      limit = APP_CONFIG.rag.maxSearchResults,
      threshold = APP_CONFIG.rag.similarityThreshold 
    } = body;

    // Validate required fields
    if (!collection_id || !user_id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields: collection_id, user_id',
          code: 'MISSING_REQUIRED_FIELDS',
          required_fields: ['collection_id', 'user_id'],
          optional_fields: ['query', 'vector', 'limit', 'threshold']
        },
        { status: 400 }
      );
    }

    // Either query or vector must be provided
    if (!query && !vector) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Either query (text) or vector (embedding) must be provided',
          code: 'MISSING_SEARCH_INPUT'
        },
        { status: 400 }
      );
    }

    // If vector is provided, validate it
    if (vector && (!Array.isArray(vector) || vector.length === 0)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Vector must be a non-empty array',
          code: 'INVALID_VECTOR_FORMAT'
        },
        { status: 400 }
      );
    }

    // If only query is provided, generate a simple vector (fallback)
    let searchVector = vector;
    if (!searchVector && query) {
      console.log('⚠️ No vector provided, generating fallback vector for query:', query.substring(0, 50));
      // Generate a simple embedding using the query text
      // Note: In a real implementation, you'd want to use the same embedding model
      searchVector = Array.from({ length: 384 }, () => Math.random() * 2 - 1);
    }

    console.log('🔍 n8n RAG search request:', {
      user_id,
      collection_id,
      query_length: query?.length || 0,
      has_vector: !!vector,
      vector_length: searchVector?.length,
      limit,
      threshold
    });

    // Find the correct collection name (handles dimension changes)
    const qdrantCollectionName = await getQdrantCollectionName(
      qdrantClient,
      user_id,
      collection_id,
      searchVector?.length
    );

    try {
      if (!qdrantCollectionName) {
        return NextResponse.json({
          success: true,
          results: [],
          message: 'Collection not found or empty',
          query: query || 'vector search',
          collection_id,
          user_id,
          total_results: 0,
          search_params: {
            limit,
            threshold,
            vector_dimensions: searchVector?.length || 0,
          },
          code: 'COLLECTION_NOT_FOUND'
        });
      }

      // Perform vector search
      const searchResult = await qdrantClient.search(qdrantCollectionName, {
        vector: searchVector,
        limit: limit,
        score_threshold: threshold,
        with_payload: true,
      });

      // Format results for n8n
      const results = searchResult.map((point) => ({
        id: point.id,
        score: point.score,
        question: point.payload?.question,
        answer: point.payload?.answer,
        created_at: point.payload?.created_at,
        metadata: {
          collection_id: point.payload?.collection_id,
          user_id: point.payload?.user_id,
        }
      }));

      console.log('✅ n8n RAG search completed:', {
        results_count: results.length,
        top_score: results[0]?.score || 0,
      });

      return NextResponse.json({
        success: true,
        results,
        query: query || 'vector search',
        collection_id,
        user_id,
        total_results: results.length,
        search_params: {
          limit,
          threshold,
          vector_dimensions: searchVector?.length || 0,
        },
        metadata: {
          collection_name: qdrantCollectionName,
          timestamp: new Date().toISOString(),
        }
      });

    } catch (qdrantError) {
      console.error('Qdrant search failed:', qdrantError);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to search in Qdrant', 
          code: 'QDRANT_SEARCH_FAILED',
          details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error' 
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in n8n rag-search API:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
