'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Title,
  Text,
  Button,
  Card,
  Group,
  Stack,
  ActionIcon,
  Menu,
  Badge,
  Modal,
  TextInput,
  Textarea,
  Breadcrumbs,
  Anchor,
  Accordion,
  Box,
  Loader,
  Center,
  Divider,
  Progress,
  Alert,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconDots,
  IconEdit,
  IconTrash,
  IconQuestionMark,
  IconArrowLeft,
  IconSearch,
  IconBrain,
  IconCheck,
  IconAlertCircle,
} from '@tabler/icons-react';
import { createQAPair, updateQAPair, deleteQAPair } from '@/lib/actions/qa-pairs';
import { generateQAEmbedding, syncToQdrant, deleteFromQdrant } from '@/lib/embedding-client';

interface Collection {
  id: string;
  name: string;
  description?: string;
  sales_instructions?: string;
  user_id: string;
  created_at: string;
}

interface QAPair {
  id: string;
  question: string;
  answer: string;
  collection_id: string;
  created_at: string;
}

interface QAPairsViewProps {
  collection: Collection;
  qaPairs: QAPair[];
}

export function QAPairsView({ collection, qaPairs: initialQAPairs }: QAPairsViewProps) {
  const [qaPairs, setQAPairs] = useState(initialQAPairs || []);

  // Add error boundary for this component
  if (!collection) {
    return (
      <Card shadow="sm" padding="xl" radius="md" withBorder>
        <Stack align="center" gap="md">
          <IconAlertCircle size={48} color="var(--mantine-color-red-6)" />
          <div style={{ textAlign: 'center' }}>
            <Text size="lg" fw={500}>Collection not found</Text>
            <Text c="dimmed">The collection you're looking for doesn't exist or you don't have access to it.</Text>
          </div>
        </Stack>
      </Card>
    );
  }
  const [opened, { open, close }] = useDisclosure(false);
  const [editingQAPair, setEditingQAPair] = useState<QAPair | null>(null);
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [embeddingProgress, setEmbeddingProgress] = useState(0);
  const [embeddingStatus, setEmbeddingStatus] = useState<'idle' | 'embedding' | 'syncing' | 'complete' | 'error'>('idle');
  const [embeddingError, setEmbeddingError] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm({
    initialValues: {
      question: '',
      answer: '',
    },
    validate: {
      question: (value) => (value.trim().length < 1 ? 'Question is required' : null),
      answer: (value) => (value.trim().length < 1 ? 'Answer is required' : null),
    },
  });

  const handleSubmit = async (values: { question: string; answer: string }) => {
    setLoading(true);
    setEmbeddingStatus('embedding');
    setEmbeddingProgress(0);
    setEmbeddingError(null);

    try {
      // Step 1: Generate embedding (20% progress)
      setEmbeddingProgress(20);
      let vector;
      try {
        vector = await generateQAEmbedding(values.question, values.answer);
        console.log('✅ Embedding generated successfully:', {
          dimensions: vector.length,
          sample_values: vector.slice(0, 3).map(v => v.toFixed(4))
        });
      } catch (embeddingError) {
        console.error('❌ Embedding generation failed:', embeddingError);
        setEmbeddingError(`Embedding generation failed: ${embeddingError instanceof Error ? embeddingError.message : 'Unknown error'}`);
        throw embeddingError; // Don't proceed with invalid vectors
      }

      // Step 2: Save to database (60% progress)
      setEmbeddingProgress(60);
      let qaPairResult;

      if (editingQAPair) {
        // Update existing Q&A pair
        qaPairResult = await updateQAPair(editingQAPair.id, values);
        if (qaPairResult.success) {
          setQAPairs(prev =>
            prev.map(qa => qa.id === editingQAPair.id
              ? { ...qa, ...values }
              : qa
            )
          );

          // Note: With qa_pair_id-based point IDs, we no longer need to delete old vectors
          // The upsert operation will automatically update the existing point
        } else {
          throw new Error(qaPairResult.error);
        }
      } else {
        // Create new Q&A pair
        qaPairResult = await createQAPair({
          ...values,
          collection_id: collection?.id || ''
        });
        if (qaPairResult.success && qaPairResult.data) {
          setQAPairs(prev => [qaPairResult.data, ...prev]);
        } else {
          throw new Error(qaPairResult.error);
        }
      }

      // Step 3: Sync to Qdrant (80% progress)
      setEmbeddingStatus('syncing');
      setEmbeddingProgress(80);

      const syncResult = await syncToQdrant({
        question: values.question,
        answer: values.answer,
        vector,
        collection_id: collection?.id || '',
        user_id: collection?.user_id || '',
        qa_pair_id: editingQAPair?.id || qaPairResult?.data?.id,
      });

      if (!syncResult.success) {
        console.warn('Qdrant sync failed:', syncResult.error);
        // Don't fail the entire operation if Qdrant sync fails
        notifications.show({
          title: 'Vector Sync Warning',
          message: `Q&A pair saved but vector sync failed: ${syncResult.error}. Search functionality may be limited for this item.`,
          color: 'yellow',
          autoClose: 8000,
        });
      } else {
        console.log('✅ Successfully synced to Qdrant');
      }

      // Step 4: Complete (100% progress)
      setEmbeddingProgress(100);
      setEmbeddingStatus('complete');

      notifications.show({
        title: 'Success',
        message: editingQAPair
          ? 'Q&A pair updated and synced successfully'
          : 'Q&A pair created and synced successfully',
        color: 'green',
      });

      // Close modal after a brief delay to show completion
      setTimeout(() => {
        handleCloseModal();
      }, 1000);

    } catch (error) {
      console.error('❌ Q&A pair creation/update failed:', error);
      setEmbeddingStatus('error');

      // Provide more specific error messages
      let errorMessage = 'Something went wrong';
      if (error instanceof Error) {
        if (error.message.includes('embedding')) {
          errorMessage = `Embedding generation failed: ${error.message}. Please check your internet connection and try again.`;
        } else if (error.message.includes('Qdrant') || error.message.includes('vector')) {
          errorMessage = `Vector storage failed: ${error.message}. The Q&A pair may have been saved but search functionality will be limited.`;
        } else if (error.message.includes('database') || error.message.includes('Supabase')) {
          errorMessage = `Database error: ${error.message}. Please try again.`;
        } else {
          errorMessage = error.message;
        }
      }

      setEmbeddingError(errorMessage);
      notifications.show({
        title: 'Q&A Creation Failed',
        message: errorMessage,
        color: 'red',
        autoClose: 10000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (qaPair: QAPair) => {
    setEditingQAPair(qaPair);
    form.setValues({
      question: qaPair.question,
      answer: qaPair.answer,
    });
    open();
  };

  const handleDelete = async (qaPair: QAPair) => {
    if (!confirm('Are you sure you want to delete this Q&A pair? This action cannot be undone.')) {
      return;
    }

    setDeleting(qaPair.id);
    try {
      // First delete from database
      const result = await deleteQAPair(qaPair.id);
      if (result.success) {
        // Then try to delete from Qdrant
        try {
          const qdrantResult = await deleteFromQdrant({
            question: qaPair.question,
            collection_id: qaPair.collection_id,
            user_id: collection?.user_id || '',
          });

          if (!qdrantResult.success) {
            console.warn('Qdrant deletion failed:', qdrantResult.error);
            notifications.show({
              title: 'Warning',
              message: 'Q&A pair deleted from database but vector deletion failed',
              color: 'yellow',
            });
          } else {
            notifications.show({
              title: 'Success',
              message: 'Q&A pair deleted successfully from both database and vector store',
              color: 'green',
            });
          }
        } catch (qdrantError) {
          console.warn('Qdrant deletion error:', qdrantError);
          notifications.show({
            title: 'Warning',
            message: 'Q&A pair deleted from database but vector deletion failed',
            color: 'yellow',
          });
        }

        setQAPairs(prev => prev.filter(qa => qa.id !== qaPair.id));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to delete Q&A pair',
        color: 'red',
      });
    } finally {
      setDeleting(null);
    }
  };

  const handleCloseModal = () => {
    close();
    setEditingQAPair(null);
    form.reset();
    setEmbeddingStatus('idle');
    setEmbeddingProgress(0);
    setEmbeddingError(null);
  };

  // Filter Q&A pairs based on search query
  const filteredQAPairs = (qaPairs || []).filter(qaPair =>
    qaPair?.question?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    qaPair?.answer?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const breadcrumbItems = [
    { title: 'Collections', href: '/dashboard/collections' },
    { title: collection?.name || 'Collection', href: '#' },
  ].map((item, index) => (
    <Anchor 
      key={index} 
      onClick={() => item.href !== '#' && router.push(item.href)}
      style={{ cursor: item.href !== '#' ? 'pointer' : 'default' }}
    >
      {item.title}
    </Anchor>
  ));

  return (
    <>
      <Stack gap="lg">
        <div>
          <Breadcrumbs mb="xs">{breadcrumbItems}</Breadcrumbs>
          <Group justify="space-between" align="flex-start">
            <div>
              <Group gap="sm" mb="xs">
                <Button
                  variant="subtle"
                  size="sm"
                  leftSection={<IconArrowLeft size="1rem" />}
                  onClick={() => router.push('/dashboard/collections')}
                >
                  Back to Collections
                </Button>
              </Group>
              <Title order={1}>{collection?.name || 'Collection'}</Title>
              {collection?.description && (
                <Text c="dimmed" size="lg">
                  {collection.description}
                </Text>
              )}
            </div>
            <Button leftSection={<IconPlus size="1rem" />} onClick={open}>
              Add Q&A Pair
            </Button>
          </Group>
        </div>

        {qaPairs.length > 0 && (
          <TextInput
            placeholder="Search questions and answers..."
            leftSection={<IconSearch size="1rem" />}
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.currentTarget.value)}
            style={{ maxWidth: 400 }}
          />
        )}

        {qaPairs.length === 0 ? (
          <Card shadow="sm" padding="xl" radius="md" withBorder>
            <Stack align="center" gap="md">
              <IconQuestionMark size={48} color="var(--mantine-color-dimmed)" />
              <div style={{ textAlign: 'center' }}>
                <Text size="lg" fw={500}>No Q&A pairs yet</Text>
                <Text c="dimmed">Add your first question and answer to get started</Text>
              </div>
              <Button leftSection={<IconPlus size="1rem" />} onClick={open}>
                Add Q&A Pair
              </Button>
            </Stack>
          </Card>
        ) : filteredQAPairs.length === 0 ? (
          <Card shadow="sm" padding="xl" radius="md" withBorder>
            <Stack align="center" gap="md">
              <IconSearch size={48} color="var(--mantine-color-dimmed)" />
              <div style={{ textAlign: 'center' }}>
                <Text size="lg" fw={500}>No matching Q&A pairs</Text>
                <Text c="dimmed">Try adjusting your search terms</Text>
              </div>
              <Button variant="subtle" onClick={() => setSearchQuery('')}>
                Clear Search
              </Button>
            </Stack>
          </Card>
        ) : (
          <Accordion variant="separated" radius="md">
            {filteredQAPairs.map((qaPair) => (
              <Accordion.Item key={qaPair.id} value={qaPair.id}>
                <Box pos="relative">
                  <Accordion.Control>
                    <Group justify="space-between" wrap="nowrap">
                      <Text fw={500} truncate style={{ flex: 1 }}>
                        {qaPair.question}
                      </Text>
                      <Badge variant="light" size="sm">
                        {new Date(qaPair.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </Badge>
                    </Group>
                  </Accordion.Control>
                  <Menu shadow="md" width={200} position="bottom-end">
                    <Menu.Target>
                      <ActionIcon
                        variant="subtle"
                        color="gray"
                        size="sm"
                        style={{
                          position: 'absolute',
                          top: '50%',
                          right: '8px',
                          transform: 'translateY(-50%)',
                          zIndex: 1,
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <IconDots size="1rem" />
                      </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<IconEdit size="0.9rem" />}
                        onClick={() => handleEdit(qaPair)}
                      >
                        Edit
                      </Menu.Item>
                      <Menu.Item
                        leftSection={
                          deleting === qaPair.id ? (
                            <Loader size="0.9rem" />
                          ) : (
                            <IconTrash size="0.9rem" />
                          )
                        }
                        color="red"
                        onClick={() => handleDelete(qaPair)}
                        disabled={deleting === qaPair.id}
                      >
                        {deleting === qaPair.id ? 'Deleting...' : 'Delete'}
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </Box>
                <Accordion.Panel>
                  <Text style={{ whiteSpace: 'pre-wrap' }}>
                    {qaPair.answer}
                  </Text>
                </Accordion.Panel>
              </Accordion.Item>
            ))}
          </Accordion>
        )}
      </Stack>

      <Modal
        opened={opened}
        onClose={handleCloseModal}
        title={editingQAPair ? 'Edit Q&A Pair' : 'Add New Q&A Pair'}
        size="lg"
        closeOnClickOutside={!loading}
        closeOnEscape={!loading}
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Question"
              placeholder="Enter your question"
              required
              disabled={loading}
              {...form.getInputProps('question')}
            />
            <Textarea
              label="Answer"
              placeholder="Enter the answer"
              required
              rows={6}
              disabled={loading}
              {...form.getInputProps('answer')}
            />

            {/* Embedding Progress Section */}
            {embeddingStatus !== 'idle' && (
              <Card withBorder p="md" bg="gray.0" style={{ backgroundColor: 'var(--mantine-color-dark-8)' }}>
                <Stack gap="sm">
                  <Group gap="xs">
                    <IconBrain size="1rem" />
                    <Text size="sm" fw={500}>
                      {embeddingStatus === 'embedding' && 'Generating embedding...'}
                      {embeddingStatus === 'syncing' && 'Syncing to vector database...'}
                      {embeddingStatus === 'complete' && 'Successfully synced!'}
                      {embeddingStatus === 'error' && 'Sync failed'}
                    </Text>
                    {embeddingStatus === 'complete' && <IconCheck size="1rem" color="green" />}
                    {embeddingStatus === 'error' && <IconAlertCircle size="1rem" color="red" />}
                  </Group>

                  <Progress
                    value={embeddingProgress}
                    color={embeddingStatus === 'error' ? 'red' : embeddingStatus === 'complete' ? 'green' : 'blue'}
                    size="sm"
                    animated={embeddingStatus === 'embedding' || embeddingStatus === 'syncing'}
                  />

                  {embeddingStatus === 'embedding' && (
                    <Text size="xs" c="dimmed">
                      Processing text with AI model...
                    </Text>
                  )}
                  {embeddingStatus === 'syncing' && (
                    <Text size="xs" c="dimmed">
                      Storing vector in Qdrant database...
                    </Text>
                  )}
                  {embeddingStatus === 'complete' && (
                    <Text size="xs" c="green">
                      Your Q&A pair is now searchable with semantic search!
                    </Text>
                  )}
                  {embeddingStatus === 'error' && embeddingError && (
                    <Alert color="red" size="sm">
                      <Text size="xs">{embeddingError}</Text>
                    </Alert>
                  )}
                </Stack>
              </Card>
            )}

            <Group justify="flex-end" gap="sm">
              <Button variant="subtle" onClick={handleCloseModal} disabled={loading}>
                Cancel
              </Button>
              <Button type="submit" loading={loading} disabled={embeddingStatus === 'complete'}>
                {loading ? (
                  embeddingStatus === 'embedding' ? 'Embedding...' :
                  embeddingStatus === 'syncing' ? 'Syncing...' :
                  'Processing...'
                ) : (
                  editingQAPair ? 'Update & Sync' : 'Add & Sync'
                )}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </>
  );
}
