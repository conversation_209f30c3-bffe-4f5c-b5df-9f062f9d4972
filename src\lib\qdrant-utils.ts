import { QdrantClient } from '@qdrant/js-client-rest';

/**
 * Get the correct Qdrant collection name for a user and collection
 * This function handles the transition from old naming (without dimensions) to new naming (with dimensions)
 */
export async function getQdrantCollectionName(
  qdrantClient: QdrantClient,
  userId: string,
  collectionId: string,
  vectorDimensions?: number
): Promise<string | null> {
  try {
    const collections = await qdrantClient.getCollections();
    const allCollections = collections.collections || [];

    // If we have vector dimensions, try the new naming convention first
    if (vectorDimensions) {
      const newCollectionName = `user_${userId}_collection_${collectionId}_dim${vectorDimensions}`;
      const newCollectionExists = allCollections.some(col => col.name === newCollectionName);
      if (newCollectionExists) {
        return newCollectionName;
      }
    }

    // Try the old naming convention (for backward compatibility)
    const oldCollectionName = `user_${userId}_collection_${collectionId}`;
    const oldCollectionExists = allCollections.some(col => col.name === oldCollectionName);
    if (oldCollectionExists) {
      return oldCollectionName;
    }

    // Try to find any collection that matches the pattern with any dimensions
    const patternRegex = new RegExp(`^user_${userId}_collection_${collectionId}(_dim\\d+)?$`);
    const matchingCollection = allCollections.find(col => patternRegex.test(col.name));
    if (matchingCollection) {
      return matchingCollection.name;
    }

    // No matching collection found
    return null;
  } catch (error) {
    console.error('Error getting Qdrant collection name:', error);
    return null;
  }
}

/**
 * Create a new Qdrant collection name with dimensions
 */
export function createQdrantCollectionName(
  userId: string,
  collectionId: string,
  vectorDimensions: number
): string {
  return `user_${userId}_collection_${collectionId}_dim${vectorDimensions}`;
}

/**
 * Extract dimensions from a collection name
 */
export function extractDimensionsFromCollectionName(collectionName: string): number | null {
  const match = collectionName.match(/_dim(\d+)$/);
  return match ? parseInt(match[1], 10) : null;
}
