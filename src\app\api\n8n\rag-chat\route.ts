import { NextRequest, NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { APP_CONFIG } from '@/lib/config';
import { getQdrantCollectionName } from '@/lib/qdrant-utils';

// Simple API key authentication for n8n
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-api-key') || request.headers.get('authorization')?.replace('Bearer ', '');
  
  // For now, we'll use a simple API key check
  // In production, you should use a proper API key management system
  const validApiKey = process.env.N8N_API_KEY || 'your-secret-api-key';
  
  return apiKey === validApiKey;
}

export async function POST(request: NextRequest) {
  try {
    // Validate API key for n8n access
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Unauthorized. Please provide a valid API key in the x-api-key header or Authorization header.' 
        },
        { status: 401 }
      );
    }

    // Check if required services are configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Qdrant is not configured. Vector search is not available.',
          code: 'QDRANT_NOT_CONFIGURED'
        },
        { status: 503 }
      );
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Gemini API key is not configured.',
          code: 'GEMINI_NOT_CONFIGURED'
        },
        { status: 503 }
      );
    }

    // Initialize clients
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: APP_CONFIG.api.chat.model });

    const body = await request.json();
    const { 
      question, 
      collection_id, 
      user_id, 
      query_vector,
      language = APP_CONFIG.rag.defaultLanguage,
      limit = APP_CONFIG.rag.maxSearchResults,
      threshold = APP_CONFIG.rag.similarityThreshold 
    } = body;

    // Validate required fields
    if (!question || !collection_id || !user_id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields: question, collection_id, user_id',
          code: 'MISSING_REQUIRED_FIELDS',
          required_fields: ['question', 'collection_id', 'user_id']
        },
        { status: 400 }
      );
    }

    // If query_vector is not provided, we'll generate it (for n8n convenience)
    let searchVector = query_vector;
    if (!searchVector) {
      // Generate a simple embedding using the question text
      // Note: In a real implementation, you'd want to use the same embedding model
      // For now, we'll create a dummy vector or skip vector search
      console.log('⚠️ No query_vector provided, using fallback approach');
      searchVector = Array.from({ length: 384 }, () => Math.random() * 2 - 1);
    }

    // Validate vector format if provided
    if (searchVector && (!Array.isArray(searchVector) || searchVector.length === 0)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'query_vector must be a non-empty array',
          code: 'INVALID_VECTOR_FORMAT'
        },
        { status: 400 }
      );
    }

    // Validate language
    const supportedLanguages = APP_CONFIG.rag.supportedLanguages.map(lang => lang.code);
    if (!supportedLanguages.includes(language)) {
      return NextResponse.json(
        { 
          success: false,
          error: `Unsupported language. Supported: ${supportedLanguages.join(', ')}`,
          code: 'UNSUPPORTED_LANGUAGE',
          supported_languages: supportedLanguages
        },
        { status: 400 }
      );
    }

    console.log('🤖 n8n RAG chat request:', {
      user_id,
      collection_id,
      question_length: question.length,
      language,
      has_vector: !!searchVector,
      vector_length: searchVector?.length,
    });

    // Find the correct collection name (handles dimension changes)
    const qdrantCollectionName = await getQdrantCollectionName(
      qdrantClient,
      user_id,
      collection_id,
      searchVector?.length
    );

    try {
      if (!qdrantCollectionName) {
        return NextResponse.json({
          success: false,
          error: 'Collection not found. Please ensure the collection exists and has Q&A pairs.',
          code: 'COLLECTION_NOT_FOUND',
          collection_name: `user_${user_id}_collection_${collection_id}`
        }, { status: 404 });
      }

      // Perform vector search
      const searchResults = await qdrantClient.search(qdrantCollectionName, {
        vector: searchVector,
        limit: limit,
        score_threshold: threshold,
        with_payload: true,
      });

      console.log('🔍 Vector search completed:', {
        results_count: searchResults.length,
        top_score: searchResults[0]?.score || 0,
      });

      // Prepare context for RAG
      const context = searchResults.map(result => 
        `Q: ${result.payload?.question}\nA: ${result.payload?.answer}`
      ).join('\n\n');

      // Generate RAG response
      let ragPrompt = '';
      if (language === 'hi-en') {
        ragPrompt = `Based on the following context, answer the question in a mix of Hindi and English (Hinglish): "${question}"

Context:
${context}

Please provide a helpful answer in Hinglish based on the context above. If the context doesn't contain relevant information, say so in Hinglish.`;
      } else if (language === 'ne-en') {
        ragPrompt = `Based on the following context, answer the question in a mix of Nepali and English: "${question}"

Context:
${context}

Please provide a helpful answer in mixed Nepali-English based on the context above. If the context doesn't contain relevant information, say so.`;
      } else {
        ragPrompt = `Based on the following context, answer the question: "${question}"

Context:
${context}

Please provide a helpful answer based on the context above. If the context doesn't contain relevant information, say so.`;
      }

      const result = await model.generateContent(ragPrompt);
      const response = result.response;
      const answer = response.text();

      console.log('✅ RAG response generated for n8n:', {
        answer_length: answer.length,
        context_results: searchResults.length,
      });

      return NextResponse.json({
        success: true,
        answer,
        question,
        language,
        context: {
          results_found: searchResults.length,
          search_results: searchResults.map(result => ({
            id: result.id,
            score: result.score,
            question: result.payload?.question,
            answer: result.payload?.answer,
          })),
          has_context: context.length > 0,
        },
        metadata: {
          collection_id,
          user_id,
          search_params: { limit, threshold },
          model: APP_CONFIG.api.chat.model,
          timestamp: new Date().toISOString(),
        },
      });

    } catch (qdrantError) {
      console.error('Qdrant search failed:', qdrantError);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to search in Qdrant', 
          code: 'QDRANT_SEARCH_FAILED',
          details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error' 
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in n8n rag-chat API:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
