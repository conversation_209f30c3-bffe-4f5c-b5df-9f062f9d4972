import { NextResponse } from 'next/server';
import { getAdminStatus } from '@/lib/admin-auth';

export async function GET() {
  try {
    const adminStatus = await getAdminStatus();
    
    return NextResponse.json({
      success: true,
      isLoggedIn: adminStatus.isLoggedIn,
      isAdmin: adminStatus.isAdmin,
      user: adminStatus.user
    });
  } catch (error) {
    console.error('Admin check error:', error);
    
    return NextResponse.json({
      success: false,
      isLoggedIn: false,
      isAdmin: false,
      error: 'Failed to check admin status'
    }, { status: 500 });
  }
}
