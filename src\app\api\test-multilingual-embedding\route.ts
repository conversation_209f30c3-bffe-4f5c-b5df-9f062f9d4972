import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';

async function runMultilingualEmbeddingTest() {
  try {
    console.log('🌍 Testing multilingual embedding functionality...');

    const testCases = [
      {
        language: 'English',
        question: 'What is the quality of the material?',
        answer: 'The material is nylon, not cotton. The back side is transparent and the front is double layer.',
        search_query: 'quality'
      },
      {
        language: 'Hindi-English',
        question: 'Material ki quality kaisi hai?',
        answer: 'Material nylon hai, cotton nahi. Back side transparent hai aur front double layer hai.',
        search_query: 'quality'
      },
      {
        language: 'Nepali-English',
        question: 'Material ko quality kasto cha?',
        answer: 'Material nylon ho, cotton hoina. Back side transparent cha ra front double layer cha.',
        search_query: 'quality'
      }
    ];

    const results = {
      openrouter_available: !!process.env.OPENROUTER_API_KEY,
      test_cases: [] as any[],
      performance: {
        total_time_ms: 0,
        average_time_per_embedding: 0,
        successful_embeddings: 0,
        failed_embeddings: 0
      },
      overall_success: false
    };

    const startTime = Date.now();

    for (const testCase of testCases) {
      console.log(`🔄 Testing ${testCase.language} embedding...`);
      
      const caseResult = {
        language: testCase.language,
        qa_embedding: null as any,
        search_embedding: null as any,
        similarity_score: null as number | null,
        success: false,
        error: null as string | null
      };

      try {
        // Test Q&A embedding
        const qaStart = Date.now();
        const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
        const qaResponse = await fetch(`${baseUrl}/api/generate-gemini-embedding`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.INTERNAL_API_TOKEN || 'internal'}`,
          },
          body: JSON.stringify({
            question: testCase.question,
            answer: testCase.answer,
            type: 'qa_pair'
          }),
        });

        if (!qaResponse.ok) {
          const errorData = await qaResponse.json();
          throw new Error(`Q&A embedding failed: ${errorData.error}`);
        }

        const qaResult = await qaResponse.json();
        const qaTime = Date.now() - qaStart;

        caseResult.qa_embedding = {
          success: qaResult.success,
          dimensions: qaResult.embedding?.length || 0,
          response_time_ms: qaTime,
          model: qaResult.model,
          tokens_used: qaResult.tokens_used
        };

        // Test search embedding
        const searchStart = Date.now();
        const searchResponse = await fetch(`${baseUrl}/api/generate-gemini-embedding`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.INTERNAL_API_TOKEN || 'internal'}`,
          },
          body: JSON.stringify({
            query: testCase.search_query,
            type: 'search_query'
          }),
        });

        if (!searchResponse.ok) {
          const errorData = await searchResponse.json();
          throw new Error(`Search embedding failed: ${errorData.error}`);
        }

        const searchResult = await searchResponse.json();
        const searchTime = Date.now() - searchStart;

        caseResult.search_embedding = {
          success: searchResult.success,
          dimensions: searchResult.embedding?.length || 0,
          response_time_ms: searchTime,
          model: searchResult.model,
          tokens_used: searchResult.tokens_used
        };

        // Calculate similarity between Q&A and search embeddings
        if (qaResult.embedding && searchResult.embedding) {
          const similarity = calculateCosineSimilarity(qaResult.embedding, searchResult.embedding);
          caseResult.similarity_score = similarity;
        }

        caseResult.success = true;
        results.performance.successful_embeddings += 2; // Q&A + search

        console.log(`✅ ${testCase.language} embedding test completed successfully`);

      } catch (error) {
        console.error(`❌ ${testCase.language} embedding test failed:`, error);
        caseResult.error = error instanceof Error ? error.message : 'Unknown error';
        results.performance.failed_embeddings += 2;
      }

      results.test_cases.push(caseResult);
    }

    const totalTime = Date.now() - startTime;
    results.performance.total_time_ms = totalTime;
    results.performance.average_time_per_embedding = totalTime / (results.performance.successful_embeddings || 1);
    results.overall_success = results.performance.successful_embeddings > 0;

    // Test cross-language similarity
    const crossLanguageTests = [];
    if (results.test_cases.length >= 2) {
      for (let i = 0; i < results.test_cases.length; i++) {
        for (let j = i + 1; j < results.test_cases.length; j++) {
          const case1 = results.test_cases[i];
          const case2 = results.test_cases[j];
          
          if (case1.success && case2.success) {
            // Get embeddings from the API responses (we'd need to store them)
            crossLanguageTests.push({
              languages: `${case1.language} vs ${case2.language}`,
              note: 'Cross-language similarity testing would require storing embeddings'
            });
          }
        }
      }
    }

    return NextResponse.json({
      success: results.overall_success,
      message: results.overall_success 
        ? 'Multilingual embedding test completed successfully'
        : 'Some multilingual embedding tests failed',
      results,
      cross_language_tests: crossLanguageTests,
      recommendations: {
        openrouter_configured: results.openrouter_available,
        embedding_service: results.openrouter_available 
          ? 'Using OpenRouter Cohere multilingual model (optimal)'
          : 'OpenRouter not configured - using browser fallback (slower)',
        multilingual_support: results.openrouter_available
          ? 'Full multilingual support available'
          : 'Limited multilingual support with browser embedding'
      }
    });

  } catch (error) {
    console.error('❌ Multilingual embedding test failed:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Multilingual embedding test execution failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate cosine similarity
function calculateCosineSimilarity(vec1: number[], vec2: number[]): number {
  if (vec1.length !== vec2.length) {
    throw new Error('Vectors must have the same length');
  }

  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;

  for (let i = 0; i < vec1.length; i++) {
    dotProduct += vec1[i] * vec2[i];
    norm1 += vec1[i] * vec1[i];
    norm2 += vec2[i] * vec2[i];
  }

  norm1 = Math.sqrt(norm1);
  norm2 = Math.sqrt(norm2);

  if (norm1 === 0 || norm2 === 0) {
    return 0;
  }

  return dotProduct / (norm1 * norm2);
}

// GET endpoint for browser testing
export async function GET() {
  return runMultilingualEmbeddingTest();
}

// POST endpoint for backward compatibility
export async function POST(request: NextRequest) {
  return runMultilingualEmbeddingTest();
}
