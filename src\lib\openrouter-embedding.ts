'use client';

/**
 * OpenRouter Cohere Multilingual Embedding Service
 * Uses cohere.embed-multilingual-v3.0 for better performance and multilingual support
 */

interface OpenRouterEmbeddingResponse {
  data: Array<{
    object: string;
    embedding: number[];
    index: number;
  }>;
  model: string;
  object: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

interface OpenRouterErrorResponse {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

/**
 * Generate embedding using OpenRouter's Cohere multilingual model
 */
export async function generateOpenRouterEmbedding(text: string): Promise<number[]> {
  try {
    console.log('🌐 Generating embedding using OpenRouter Cohere multilingual model...');
    
    const startTime = Date.now();
    
    const response = await fetch('https://openrouter.ai/api/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'Odude Chat - RAG System',
      },
      body: JSON.stringify({
        model: 'cohere/embed-multilingual-v3.0',
        input: text,
        encoding_format: 'float',
      }),
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      const errorData: OpenRouterErrorResponse = await response.json();
      
      // Handle specific error types
      if (response.status === 429) {
        throw new Error(`Rate limited: ${errorData.error.message}. Please try again in a moment.`);
      } else if (response.status === 401) {
        throw new Error('Invalid OpenRouter API key. Please check your configuration.');
      } else if (response.status === 402) {
        throw new Error('Insufficient credits on OpenRouter account.');
      } else {
        throw new Error(`OpenRouter API error (${response.status}): ${errorData.error.message}`);
      }
    }

    const result: OpenRouterEmbeddingResponse = await response.json();
    
    if (!result.data || result.data.length === 0) {
      throw new Error('No embedding data received from OpenRouter');
    }

    const embedding = result.data[0].embedding;
    
    if (!embedding || embedding.length === 0) {
      throw new Error('Empty embedding received from OpenRouter');
    }

    // Validate embedding values
    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Invalid embedding values received from OpenRouter');
    }

    console.log('✅ OpenRouter embedding generated successfully:', {
      model: result.model,
      dimensions: embedding.length,
      response_time_ms: responseTime,
      tokens_used: result.usage?.total_tokens || 0,
      text_length: text.length,
      magnitude: Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0)).toFixed(4)
    });

    return embedding;

  } catch (error) {
    console.error('❌ OpenRouter embedding generation failed:', error);
    
    // Re-throw with more context
    if (error instanceof Error) {
      throw new Error(`OpenRouter embedding failed: ${error.message}`);
    } else {
      throw new Error('OpenRouter embedding failed: Unknown error');
    }
  }
}

/**
 * Generate Q&A embedding using OpenRouter (combines question and answer)
 */
export async function generateOpenRouterQAEmbedding(question: string, answer: string = ''): Promise<number[]> {
  const combinedText = answer 
    ? `Question: ${question}\nAnswer: ${answer}`
    : `Query: ${question}`;
    
  return generateOpenRouterEmbedding(combinedText);
}

/**
 * Generate search embedding using OpenRouter (for search queries)
 */
export async function generateOpenRouterSearchEmbedding(query: string): Promise<number[]> {
  const searchText = `Query: ${query}`;
  return generateOpenRouterEmbedding(searchText);
}

/**
 * Test OpenRouter API connectivity and model availability
 */
export async function testOpenRouterConnection(): Promise<{
  success: boolean;
  model: string;
  dimensions?: number;
  response_time_ms?: number;
  error?: string;
}> {
  try {
    console.log('🧪 Testing OpenRouter connection...');
    
    const testText = "Hello, this is a test.";
    const startTime = Date.now();
    
    const embedding = await generateOpenRouterEmbedding(testText);
    const responseTime = Date.now() - startTime;
    
    return {
      success: true,
      model: 'cohere/embed-multilingual-v3.0',
      dimensions: embedding.length,
      response_time_ms: responseTime
    };
    
  } catch (error) {
    console.error('❌ OpenRouter connection test failed:', error);
    
    return {
      success: false,
      model: 'cohere/embed-multilingual-v3.0',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check if OpenRouter is properly configured
 */
export function isOpenRouterConfigured(): boolean {
  return !!process.env.OPENROUTER_API_KEY;
}

/**
 * Get OpenRouter configuration status
 */
export function getOpenRouterConfig() {
  return {
    configured: isOpenRouterConfigured(),
    model: 'cohere/embed-multilingual-v3.0',
    features: [
      'Multilingual support (100+ languages)',
      'Hindi-English support',
      'Nepali-English support', 
      'Fast API-based generation',
      'High-quality embeddings'
    ]
  };
}
