import { <PERSON><PERSON>, But<PERSON>, Stack, Title, Text, Card } from '@mantine/core';
import { IconLock, IconHome } from '@tabler/icons-react';
import Link from 'next/link';

interface AdminGuardProps {
  children: React.ReactNode;
  isAdmin: boolean;
  fallback?: React.ReactNode;
}

export function AdminGuard({ children, isAdmin, fallback }: AdminGuardProps) {
  if (isAdmin) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  return (
    <Stack gap="lg" align="center" style={{ minHeight: '60vh', justifyContent: 'center' }}>
      <Card shadow="sm" padding="xl" radius="md" withBorder style={{ maxWidth: 500, textAlign: 'center' }}>
        <Stack gap="md" align="center">
          <IconLock size={64} color="var(--mantine-color-red-6)" />
          
          <Title order={2} c="red">
            Access Restricted
          </Title>
          
          <Text size="lg" c="dimmed">
            This page is only accessible to administrators.
          </Text>
          
          <Text size="sm" c="dimmed">
            You need admin privileges to view this content. If you believe you should have access, 
            please contact your system administrator.
          </Text>
          
          <Button
            component={Link}
            href="/dashboard"
            leftSection={<IconHome size="1rem" />}
            variant="filled"
            size="md"
          >
            Return to Dashboard
          </Button>
        </Stack>
      </Card>
    </Stack>
  );
}

interface AdminOnlyProps {
  children: React.ReactNode;
  isAdmin: boolean;
  showMessage?: boolean;
}

export function AdminOnly({ children, isAdmin, showMessage = false }: AdminOnlyProps) {
  if (isAdmin) {
    return <>{children}</>;
  }

  if (showMessage) {
    return (
      <Alert icon={<IconLock size="1rem" />} color="red" variant="light">
        <Text size="sm">
          This feature is only available to administrators.
        </Text>
      </Alert>
    );
  }

  return null;
}
