export const APP_CONFIG = {
  name: 'odude-chat',
  description: 'Multi-user Q&A Collections SaaS',
  theme: {
    primaryColor: 'blue',
    defaultColorScheme: 'dark' as const,
  },
  routes: {
    home: '/',
    dashboard: '/dashboard',
    login: '/auth/signin',
    collections: '/collections',
  },
  // API Provider Configuration - Change providers and models from here
  api: {
    // Embedding Provider Configuration
    embedding: {
      provider: 'gemini', // Change this to 'gemini'
      model: 'text-embedding-004', // Specify the embedding model
      apiKey: process.env.GEMINI_API_KEY, // Use your existing Gemini API key
      endpoint: 'https://generativelanguage.googleapis.com/v1beta', // Update the endpoint
      features: ['Multilingual support']
    },
    // Chat Provider Configuration
    chat: {
      provider: 'gemini', // 'gemini' | 'openai' | 'openrouter'
      model: 'gemini-1.5-flash',
      apiKey: process.env.GEMINI_API_KEY,
      maxTokens: 1000,
      temperature: 0.7,
    }
  },
  rag: {
    // Supported language tones for RAG chatbot
    supportedLanguages: [
      { code: 'hi-en', name: 'Hindi-English', description: 'Mixed Hindi and English responses' },
      { code: 'ne-en', name: 'Nepali-English', description: 'Mixed Nepali and English responses' },
      { code: 'en', name: 'English', description: 'Pure English responses' },
    ],
    // Default settings
    defaultLanguage: 'en',
    maxSearchResults: 5,
    similarityThreshold: 0.5, // Lowered from 0.7 to 0.5 for better recall
  },
} as const;