// Test script to verify our changes
const { APP_CONFIG } = require('./src/lib/config.ts');
const { generateSimpleId } = require('./src/lib/id-generator.ts');

console.log('🧪 Testing configuration changes...');

// Test 1: Check if config is properly set up
console.log('\n1. Testing centralized config:');
console.log('Embedding provider:', APP_CONFIG.api.embedding.provider);
console.log('Embedding model:', APP_CONFIG.api.embedding.model);
console.log('Chat provider:', APP_CONFIG.api.chat.provider);
console.log('Chat model:', APP_CONFIG.api.chat.model);

// Test 2: Check simple ID generation
console.log('\n2. Testing simple ID generation:');
for (let i = 0; i < 5; i++) {
  const id = generateSimpleId();
  console.log(`Generated ID ${i + 1}: ${id} (length: ${id.length})`);
}

console.log('\n✅ All tests completed!');
