# Testing Guide for odude-chat

This document provides comprehensive information about all the tests available in the odude-chat application, how they work, and how to run them.

## Overview

The odude-chat application includes multiple testing endpoints and utilities to verify system functionality, from basic database connections to complex RAG (Retrieval-Augmented Generation) workflows.

Invoke-WebRequest -Uri "http://localhost:3000/api/n8n/6699858145" -Method POST -ContentType "application/json" -Body '{"question": "price"}'

-----------------------
Invoke-WebRequest -Uri "http://localhost:3000/api/n8n/6699858145" -Method POST -ContentType "application/json" -Body '{"question": "price", "threshold": 0.3}'

-----
Invoke-WebRequest -Uri "http://localhost:3000/api/n8n/6699858145" -Method POST -ContentType "application/json" -Body '{"question": "how much"}' | Select-Object -ExpandProperty Content

---

 try { Invoke-WebRequest -Uri "http://localhost:3000/api/test-multilingual-embedding" -Method GET } catch { $_.Exception.Response.GetResponseStream() | ForEach-Object { $reader = New-Object Sy
stem.IO.StreamReader($_); $reader.ReadToEnd() } }

## Test Categories

### 1. Database Tests
- **Basic Connection**: Tests Supabase database connectivity
- **Table Structure**: Validates database schema and table relationships
- **Data Integrity**: Ensures proper data flow between tables

### 2. Vector Database Tests
- **Qdrant Connection**: Tests vector database connectivity
- **Collection Management**: Validates vector collection operations
- **Vector Search**: Tests similarity search functionality

### 3. AI/ML Tests
- **Embedding Generation**: Tests text-to-vector conversion
- **Multilingual Support**: Validates cross-language embedding quality
- **RAG Chat**: Tests complete question-answering workflow

### 4. API Integration Tests
- **n8n Compatibility**: Tests automation platform integration
- **Authentication**: Validates API key and session management
- **Error Handling**: Tests graceful failure scenarios

## Available Test Endpoints

### Core System Tests

#### 1. Database Connection Test
**Endpoint**: `GET /api/test`
**Purpose**: Tests basic database connectivity and table structure

```bash
# Test via curl
curl http://localhost:3000/api/test

#Invoke-RestMethod -Uri "http://localhost:3000/api/status" -Method GET

# Test via browser
# Navigate to: http://localhost:3000/api/test
```

**Expected Response**:
```json
{
  "success": true,
  "tests": {
    "databaseConnection": true,
    "tableStructure": true
  },
  "message": "All tests passed! Database is ready."
}
```

#### 2. Qdrant Vector Database Test
**Endpoint**: `GET /api/test-qdrant`
**Purpose**: Tests Qdrant vector database connection and collections

```bash
# Test via curl
curl http://localhost:3000/api/test-qdrant
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Qdrant connection successful",
  "collections": [...],
  "qdrantUrl": "your-qdrant-url",
  "hasApiKey": true,
  "configured": true
}
```

### AI/ML Tests

#### 3. Embedding Generation Test
**Endpoint**: `POST /api/test-embedding-status`
**Purpose**: Tests text embedding generation capabilities

```bash
# Test via curl
curl -X POST http://localhost:3000/api/test-embedding-status \
  -H "Content-Type: application/json" \
  -d '{"query": "Test embedding generation", "type": "search_query"}'
```

#### 4. Multilingual Embedding Test
**Endpoint**: `POST /api/test-multilingual-embedding`
**Purpose**: Tests embedding quality across different languages

```bash
# Test via curl
curl -X POST http://localhost:3000/api/test-multilingual-embedding
```

**Test Cases**:
- English: "What is the price of this dress?"
- Hindi-English: "Dress ka price kya hai?"
- Nepali-English: "Yo dress ko price kati ho?"

#### 5. RAG Chat Functionality Test
**Endpoint**: `POST /api/test-rag-chat`
**Purpose**: Tests complete RAG workflow from Q&A creation to answer generation

```bash
# Test via curl
curl -X POST http://localhost:3000/api/test-rag-chat
```

**Workflow Tested**:
1. Creates test collection in Qdrant
2. Adds Q&A pairs with embeddings
3. Performs vector search
4. Generates AI response
5. Cleans up test data

### Integration Tests

#### 6. Q&A Sync Workflow Test
**Endpoint**: `POST /api/test-qa-sync`
**Purpose**: Tests Q&A pair synchronization with vector database

```bash
# Test via curl
curl -X POST http://localhost:3000/api/test-qa-sync
```

#### 7. Complete RAG Workflow Test
**Endpoint**: `POST /api/test-complete-rag-workflow`
**Purpose**: Tests end-to-end RAG functionality with user authentication

```bash
# Test via curl (requires authentication)
curl -X POST http://localhost:3000/api/test-complete-rag-workflow \
  -H "Cookie: next-auth.session-token=your-session-token"
```

### n8n Integration Tests

#### 8. n8n Status Check
**Endpoint**: `GET /api/n8n/status`
**Purpose**: Tests n8n API compatibility and service health

```bash
# Test via curl (requires API key)
curl -H "x-api-key: your-api-key" http://localhost:3000/api/n8n/status
```

#### 9. n8n RAG Chat Test
**Endpoint**: `POST /api/n8n/rag-chat`
**Purpose**: Tests n8n-compatible RAG chat functionality

```bash
# Test via curl
curl -X POST http://localhost:3000/api/n8n/rag-chat \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "question": "What is the price?",
    "collection_id": "your-collection-id",
    "user_id": "your-user-id"
  }'
```

### Browser-Based Tests

#### 10. Transformers Library Test
**Endpoint**: `GET|POST /api/test-transformers`
**Purpose**: Tests client-side transformers library functionality

```bash
# Get test info
curl http://localhost:3000/api/test-transformers

# Run test
curl -X POST http://localhost:3000/api/test-transformers
```

#### 11. Embedding Debug Test
**Endpoint**: `GET /api/test-embedding-debug`
**Purpose**: Provides debugging information for client-side embedding issues

```bash
# Get debug info
curl http://localhost:3000/api/test-embedding-debug
```

## Running Tests via Dashboard

### System Status Page
Navigate to: `http://localhost:3000/dashboard/status`

This page automatically runs multiple tests and displays:
- Overall system health
- Individual service status
- Real-time diagnostics
- Setup instructions

### Manual Test Execution
1. **Login to Dashboard**: Navigate to `/dashboard`
2. **Access Status Page**: Click on "Status" or go to `/dashboard/status`
3. **View Test Results**: All tests run automatically on page load
4. **Refresh for Updates**: Reload page to re-run tests

## Command Line Testing

### Using Node.js Scripts

#### Test Qdrant Sync (Direct)
```bash
node test-qdrant-sync.js
```

#### Test Sync with Authentication
```bash
node test-sync-direct.js
```

#### Test Configuration Changes
```bash
node test-changes.js
```

### Using curl Commands

#### Basic Health Check
```bash
# Test all core endpoints
curl http://localhost:3000/api/test
curl http://localhost:3000/api/test-qdrant
curl -X POST http://localhost:3000/api/test-rag-chat
```

#### Test with Authentication
```bash
# Get session first
SESSION=$(curl -s http://localhost:3000/api/auth/session | jq -r '.user.id')

# Run authenticated tests
curl -X POST http://localhost:3000/api/test-complete-rag-workflow \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

## Environment Setup for Testing

### Required Environment Variables
```bash
# Database
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-key

# AI Services
GEMINI_API_KEY=your-gemini-api-key
OPENROUTER_API_KEY=your-openrouter-key  # Optional, for better multilingual support

# Vector Database
QDRANT_URL=your-qdrant-url
QDRANT_API_KEY=your-qdrant-key  # Optional

# n8n Integration
N8N_API_KEY=your-n8n-api-key

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
```

### Development Server
```bash
# Start development server
npm run dev

# Server will be available at http://localhost:3000
```

## Test Results Interpretation

### Success Indicators
- ✅ **Green Status**: Service is healthy and working correctly
- ⚠️ **Yellow Status**: Service has warnings but is functional
- ❌ **Red Status**: Service has errors and may not work properly

### Common Issues and Solutions

#### Database Connection Issues
- **Problem**: "Database connection error"
- **Solution**: Check Supabase URL and API key in environment variables

#### Vector Database Issues
- **Problem**: "Qdrant connection failed"
- **Solution**: Verify Qdrant URL and ensure service is running

#### AI API Issues
- **Problem**: "Gemini API key test failed"
- **Solution**: Verify API key validity and check quota limits

#### Embedding Issues
- **Problem**: "Failed to generate embedding"
- **Solution**: Check if OpenRouter API key is configured, or verify browser supports WebAssembly

## Troubleshooting

### Debug Mode
Enable detailed logging by setting:
```bash
NODE_ENV=development
```

### Test Isolation
Each test creates and cleans up its own test data to avoid conflicts.

### Performance Monitoring
Tests include timing information to help identify performance bottlenecks.

## Contributing

When adding new tests:
1. Follow the existing naming convention: `/api/test-{feature-name}`
2. Include proper error handling and cleanup
3. Add documentation to this file
4. Ensure tests are idempotent (can be run multiple times safely)

## Support

For issues with tests:
1. Check the `/dashboard/status` page for detailed diagnostics
2. Review browser console for client-side errors
3. Check server logs for API errors
4. Verify all environment variables are properly set
