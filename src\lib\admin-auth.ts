import 'server-only';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { APP_CONFIG } from './config';

export interface AdminSession {
  user: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
  isAdmin: true;
}

/**
 * Check if a user email is an admin
 */
export function isAdminEmail(email: string | null | undefined): boolean {
  if (!email) return false;
  
  const adminEmail = process.env.ADMIN_EMAIL;
  if (!adminEmail) {
    console.warn('ADMIN_EMAIL environment variable is not set');
    return false;
  }
  
  return email.toLowerCase() === adminEmail.toLowerCase();
}

/**
 * Get the current session and check if user is admin
 */
export async function getAdminSession(): Promise<AdminSession | null> {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.email) {
    return null;
  }
  
  if (!isAdminEmail(session.user.email)) {
    return null;
  }
  
  return {
    user: session.user,
    isAdmin: true
  };
}

/**
 * Require admin authentication - redirect to login if not authenticated or not admin
 */
export async function requireAdminAuth(): Promise<AdminSession> {
  const session = await getServerSession(authOptions);
  
  // If not logged in at all, redirect to login
  if (!session?.user) {
    redirect(APP_CONFIG.routes.login);
  }
  
  // If logged in but not admin, redirect to dashboard with error
  if (!isAdminEmail(session.user.email)) {
    redirect('/dashboard?error=access_denied');
  }
  
  return {
    user: session.user,
    isAdmin: true
  };
}

/**
 * Check if current user is admin (for client-side usage)
 */
export async function checkIsAdmin(): Promise<boolean> {
  const session = await getServerSession(authOptions);
  return isAdminEmail(session?.user?.email);
}

/**
 * Get admin status for current user
 */
export async function getAdminStatus(): Promise<{
  isLoggedIn: boolean;
  isAdmin: boolean;
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
}> {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    return {
      isLoggedIn: false,
      isAdmin: false
    };
  }
  
  const isAdmin = isAdminEmail(session.user.email);
  
  return {
    isLoggedIn: true,
    isAdmin,
    user: session.user
  };
}
