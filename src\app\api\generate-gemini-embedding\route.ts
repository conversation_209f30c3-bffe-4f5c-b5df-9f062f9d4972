import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { APP_CONFIG } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    // Check for internal API token for server-to-server calls
    const authHeader = request.headers.get('authorization');
    const isInternalCall = authHeader === `Bearer ${process.env.INTERNAL_API_TOKEN || 'internal'}`;

    // Authenticate user (skip for internal calls)
    let user = null;
    if (!isInternalCall) {
      user = await getCurrentUser();
    }
    
    const body = await request.json();
    const { question, answer, query, type = 'qa_pair' } = body;

    // Validate input - either question or query must be provided
    if (!question && !query) {
      return NextResponse.json(
        { error: 'Either question or query is required' },
        { status: 400 }
      );
    }

    // Check if <PERSON> is configured
    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          error: 'Gemini API key not configured',
          details: 'Please set GEMINI_API_KEY environment variable'
        },
        { status: 503 }
      );
    }

    // Prepare text based on type with enhanced multilingual support
    let text: string;
    if (type === 'search_query' || query) {
      const searchText = query || question;
      // Enhance search queries with semantic variations and multilingual synonyms
      const enhancedQuery = enhanceSearchQuery(searchText);
      text = `Query: ${enhancedQuery}`;
    } else {
      // For Q&A pairs - enhance with semantic keywords
      const enhancedQuestion = enhanceQuestionText(question);
      const enhancedAnswer = answer ? enhanceAnswerText(answer) : '';
      text = enhancedAnswer
        ? `Question: ${enhancedQuestion}\nAnswer: ${enhancedAnswer}`
        : `Query: ${enhancedQuestion}`;
    }

    console.log(`🌐 Generating Gemini embedding for ${type}...`);
    console.log('📝 Text to embed:', text.substring(0, 100) + '...');

    const startTime = Date.now();

    // Call Gemini API for embeddings using centralized config
    const embeddingConfig = APP_CONFIG.api.embedding;
    const response = await fetch(`${embeddingConfig.endpoint}/models/${embeddingConfig.model}:embedContent?key=${embeddingConfig.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: `models/${embeddingConfig.model}`,
        content: {
          parts: [{ text }]
        }
      }),
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Gemini API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      
      return NextResponse.json(
        { 
          error: 'Gemini API request failed',
          details: errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status
        },
        { status: response.status }
      );
    }

    const result = await response.json();
    
    // Extract embedding from Gemini response
    if (!result.embedding || !result.embedding.values) {
      console.error('❌ Invalid Gemini response structure:', result);
      return NextResponse.json(
        { 
          error: 'Invalid response from Gemini API',
          details: 'Expected embedding.values in response'
        },
        { status: 500 }
      );
    }

    const embedding = result.embedding.values;

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      return NextResponse.json(
        { 
          error: 'Generated embedding is empty',
          details: 'Gemini API returned empty embedding vector'
        },
        { status: 500 }
      );
    }
    
    if (embedding.some((val: number) => !isFinite(val))) {
      return NextResponse.json(
        { 
          error: 'Generated embedding contains invalid values',
          details: 'Embedding vector contains NaN or infinite values'
        },
        { status: 500 }
      );
    }

    console.log('✅ Gemini embedding generated successfully:', {
      model: embeddingConfig.model,
      dimensions: embedding.length,
      response_time_ms: responseTime,
      text_length: text.length,
      type,
      user_id: user?.id || 'internal'
    });

    return NextResponse.json({
      success: true,
      embedding,
      dimensions: embedding.length,
      method: 'gemini-api',
      model: embeddingConfig.model,
      text_length: text.length,
      response_time_ms: responseTime,
      type,
      features: embeddingConfig.features
    });

  } catch (error) {
    console.error('❌ Gemini embedding generation failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate Gemini embedding',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Enhance search queries with multilingual synonyms and semantic variations
 */
function enhanceSearchQuery(query: string): string {
  const lowerQuery = query.toLowerCase().trim();

  // Define multilingual synonym mappings
  const synonymMappings: Record<string, string[]> = {
    // Price-related terms
    'price': ['cost', 'rate', 'amount', 'fee', 'charge', 'value', 'worth'],
    'kati': ['how much', 'price', 'cost', 'amount', 'kitna', 'कति'],
    'ho': ['is', 'hai', 'छ', 'cha'],
    'kitna': ['how much', 'kati', 'price', 'cost'],
    'paisa': ['money', 'price', 'cost', 'rupees', 'पैसा'],
    'dam': ['price', 'cost', 'rate', 'दाम'],

    // Quality-related terms
    'quality': ['material', 'fabric', 'texture', 'गुणस्तर', 'gunasthara'],
    'material': ['fabric', 'cloth', 'quality', 'सामग्री'],
    'kasto': ['how', 'what kind', 'कस्तो'],

    // Size-related terms
    'size': ['measurement', 'dimension', 'साइज'],
    'kati thulo': ['how big', 'size', 'dimension'],

    // Color-related terms
    'color': ['colour', 'rang', 'रंग'],
    'ke rang': ['what color', 'which color', 'कुन रंग'],

    // General question words
    'what': ['ke', 'कुन', 'कस्तो'],
    'how': ['kasto', 'kasari', 'कसरी', 'कस्तो'],
    'where': ['kaha', 'कहाँ'],
    'when': ['kaha', 'कहिले'],
    'why': ['kina', 'किन']
  };

  // Start with the original query
  let enhancedTerms = [lowerQuery];

  // Add synonyms for each word in the query
  const words = lowerQuery.split(/\s+/);
  for (const word of words) {
    if (synonymMappings[word]) {
      enhancedTerms.push(...synonymMappings[word]);
    }

    // Handle common Nepali/Hindi phrases
    if (word === 'kati' && words.includes('ho')) {
      enhancedTerms.push('how much', 'price', 'cost', 'amount');
    }
    if (word === 'kitna' && words.includes('hai')) {
      enhancedTerms.push('how much', 'price', 'cost', 'amount');
    }
  }

  // Handle common phrases
  if (lowerQuery.includes('kati ho') || lowerQuery.includes('कति छ')) {
    enhancedTerms.push('how much', 'price', 'cost', 'amount', 'rate');
  }
  if (lowerQuery.includes('kitna hai') || lowerQuery.includes('कितना है')) {
    enhancedTerms.push('how much', 'price', 'cost', 'amount', 'rate');
  }
  if (lowerQuery.includes('how much')) {
    enhancedTerms.push('price', 'cost', 'kati ho', 'kitna hai');
  }

  // Remove duplicates and join
  const uniqueTerms = [...new Set(enhancedTerms)];
  return uniqueTerms.join(' ');
}

/**
 * Enhance question text with semantic keywords
 */
function enhanceQuestionText(question: string): string {
  const lowerQuestion = question.toLowerCase();
  let enhanced = question;

  // Add semantic context based on question content
  if (lowerQuestion.includes('price') || lowerQuestion.includes('cost')) {
    enhanced += ' amount money rate fee charge value worth kati ho kitna hai';
  }
  if (lowerQuestion.includes('quality') || lowerQuestion.includes('material')) {
    enhanced += ' fabric texture cloth gunasthara';
  }
  if (lowerQuestion.includes('size') || lowerQuestion.includes('dimension')) {
    enhanced += ' measurement big small kati thulo';
  }
  if (lowerQuestion.includes('color') || lowerQuestion.includes('colour')) {
    enhanced += ' rang shade hue ke rang';
  }

  return enhanced;
}

/**
 * Enhance answer text with semantic keywords
 */
function enhanceAnswerText(answer: string): string {
  const lowerAnswer = answer.toLowerCase();
  let enhanced = answer;

  // Add semantic context based on answer content
  if (/\d+/.test(answer) && (lowerAnswer.includes('rs') || lowerAnswer.includes('rupee') || lowerAnswer.includes('₹'))) {
    enhanced += ' price cost amount money rate fee charge value worth';
  }
  if (lowerAnswer.includes('cotton') || lowerAnswer.includes('nylon') || lowerAnswer.includes('silk')) {
    enhanced += ' material fabric quality texture cloth';
  }
  if (lowerAnswer.includes('small') || lowerAnswer.includes('medium') || lowerAnswer.includes('large') || lowerAnswer.includes('xl')) {
    enhanced += ' size measurement dimension';
  }
  if (lowerAnswer.includes('red') || lowerAnswer.includes('blue') || lowerAnswer.includes('green') || lowerAnswer.includes('black') || lowerAnswer.includes('white')) {
    enhanced += ' color colour rang shade';
  }

  return enhanced;
}
