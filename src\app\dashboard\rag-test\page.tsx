import { getCurrentUser } from '@/lib/auth';
import { getAdminStatus } from '@/lib/admin-auth';
import { AdminGuard } from '@/components/admin/admin-guard';
import { supabase } from '@/lib/supabase';
import { Container, Title, Text, Stack, Alert } from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';
import { RAGChatInterface } from '@/components/rag-chat/rag-chat-interface';

export default async function RAGTestPage() {
  const user = await getCurrentUser();

  // Check admin status
  const adminStatus = await getAdminStatus();

  // Get user's collections
  const { data: collections, error } = await supabase
    .from('collections')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching collections:', error);
  }

  const hasCollections = collections && collections.length > 0;
  const firstCollection = collections?.[0];

  return (
    <AdminGuard isAdmin={adminStatus.isAdmin}>
      <Container size="lg" py="xl">
        <Stack gap="lg">
          <div>
            <Title order={1}>RAG Chat & Search Test</Title>
            <Text c="dimmed" mt="sm">
              Test the RAG (Retrieval-Augmented Generation) functionality with your Q&A collections.
            </Text>
          </div>

        <Alert icon={<IconInfoCircle size={16} />} color="blue">
          <Stack gap="xs">
            <Text fw={500}>How to use RAG Chat & Search:</Text>
            <Text size="sm">
              1. <strong>Chat Mode:</strong> Ask questions and get AI-generated answers based on your Q&A collection context
            </Text>
            <Text size="sm">
              2. <strong>Search Mode:</strong> Find similar Q&A pairs using semantic vector search
            </Text>
            <Text size="sm">
              3. <strong>Language Support:</strong> Choose between English, Hindi-English, or Nepali-English responses
            </Text>
            <Text size="sm">
              4. Make sure you have some Q&A pairs in your collection and that Qdrant sync is working
            </Text>
          </Stack>
        </Alert>

        <Alert color="yellow">
          <Stack gap="xs">
            <Text fw={500}>⚠️ Quota Limitation Notice:</Text>
            <Text size="sm">
              If you encounter "quota exceeded" errors in Chat mode, this is due to Gemini API free tier limits (50 requests/day).
            </Text>
            <Text size="sm">
              <strong>Workaround:</strong> Use Search mode instead - it works independently and doesn't consume chat API quota.
            </Text>
            <Text size="sm">
              The quota resets every 24 hours. For production use, consider upgrading to a paid Gemini API plan.
            </Text>
          </Stack>
        </Alert>

        {!hasCollections ? (
          <Alert color="yellow">
            <Text fw={500}>No collections found</Text>
            <Text size="sm">
              You need to create a collection and add some Q&A pairs first. 
              Go to <a href="/dashboard/collections">Collections</a> to get started.
            </Text>
          </Alert>
        ) : (
          <Stack gap="md">
            <Alert color="green">
              <Text fw={500}>Testing with collection: {firstCollection.name}</Text>
              <Text size="sm">
                Collection ID: {firstCollection.id}
              </Text>
              <Text size="sm">
                Make sure this collection has Q&A pairs that are synced to Qdrant for best results.
              </Text>
            </Alert>

            <RAGChatInterface 
              collection_id={firstCollection.id}
              user_id={user.id}
            />
          </Stack>
        )}

        <Alert color="gray">
          <Stack gap="xs">
            <Text fw={500}>Configuration Status:</Text>
            <Text size="sm">
              • <strong>COHERE_API_KEY:</strong> {process.env.COHERE_API_KEY ? '✅ Configured (Primary embedding service)' : '⚠️ Missing - Using browser fallback'}
            </Text>
            <Text size="sm">
              • <strong>QDRANT_URL:</strong> {process.env.QDRANT_URL ? '✅ Configured' : '❌ Missing'}
            </Text>
            <Text size="sm">
              • <strong>QDRANT_API_KEY:</strong> {process.env.QDRANT_API_KEY ? '✅ Configured' : '❌ Missing'}
            </Text>
            <Text size="sm">
              • <strong>GEMINI_API_KEY:</strong> {process.env.GEMINI_API_KEY ? '✅ Configured' : '❌ Missing - Add your Gemini API key to .env.local'}
            </Text>
            {process.env.COHERE_API_KEY && (
              <Text size="xs" c="dimmed" mt="xs">
                🌐 Using Cohere's multilingual embedding model (embed-multilingual-v3.0) for fast, high-quality embeddings with Hindi-English and Nepali-English support.
              </Text>
            )}
            {!process.env.COHERE_API_KEY && (
              <Text size="xs" c="dimmed" mt="xs">
                ⚠️ Cohere not configured. Using browser-based embedding (slower). Add COHERE_API_KEY to .env.local for better performance.
              </Text>
            )}
          </Stack>
        </Alert>
      </Stack>
    </Container>
    </AdminGuard>
  );
}
