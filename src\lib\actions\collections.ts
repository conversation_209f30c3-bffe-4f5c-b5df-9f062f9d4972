'use server';

import { revalidatePath } from 'next/cache';
import { supabase } from '@/lib/supabase';
import { getCurrentUser } from '@/lib/auth';
import { generateCollectionId } from '@/lib/id-generator';

export async function createCollection(data: {
  name: string;
  description?: string;
  sales_instructions?: string;
  user_id: string;
}) {
  try {
    const user = await getCurrentUser();
    
    // Ensure the user can only create collections for themselves
    if (user.id !== data.user_id) {
      return { success: false, error: 'Unauthorized' };
    }

    const { data: collection, error } = await supabase
      .from('collections')
      .insert({
        id: generateCollectionId(),
        name: data.name.trim(),
        description: data.description?.trim() || null,
        sales_instructions: data.sales_instructions?.trim() || null,
        user_id: data.user_id,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating collection:', error);
      return { success: false, error: 'Failed to create collection' };
    }

    revalidatePath('/dashboard/collections');
    return { success: true, data: collection };
  } catch (error) {
    console.error('Error in createCollection:', error);
    return { success: false, error: 'Something went wrong' };
  }
}

export async function updateCollection(
  collectionId: string,
  data: {
    name: string;
    description?: string;
    sales_instructions?: string;
  }
) {
  try {
    const user = await getCurrentUser();

    // First, verify the collection belongs to the current user
    const { data: existingCollection, error: fetchError } = await supabase
      .from('collections')
      .select('user_id')
      .eq('id', collectionId)
      .single();

    if (fetchError || !existingCollection) {
      return { success: false, error: 'Collection not found' };
    }

    if (existingCollection.user_id !== user.id) {
      return { success: false, error: 'Unauthorized' };
    }

    const { error } = await supabase
      .from('collections')
      .update({
        name: data.name.trim(),
        description: data.description?.trim() || null,
        sales_instructions: data.sales_instructions?.trim() || null,
      })
      .eq('id', collectionId);

    if (error) {
      console.error('Error updating collection:', error);
      return { success: false, error: 'Failed to update collection' };
    }

    revalidatePath('/dashboard/collections');
    return { success: true };
  } catch (error) {
    console.error('Error in updateCollection:', error);
    return { success: false, error: 'Something went wrong' };
  }
}

export async function deleteCollection(collectionId: string) {
  try {
    const user = await getCurrentUser();

    // First, verify the collection belongs to the current user
    const { data: existingCollection, error: fetchError } = await supabase
      .from('collections')
      .select('user_id')
      .eq('id', collectionId)
      .single();

    if (fetchError || !existingCollection) {
      return { success: false, error: 'Collection not found' };
    }

    if (existingCollection.user_id !== user.id) {
      return { success: false, error: 'Unauthorized' };
    }

    // Delete all Q&A pairs in this collection first
    const { error: qaPairsError } = await supabase
      .from('qa_pairs')
      .delete()
      .eq('collection_id', collectionId);

    if (qaPairsError) {
      console.error('Error deleting Q&A pairs:', qaPairsError);
      return { success: false, error: 'Failed to delete collection contents' };
    }

    // Then delete the collection
    const { error } = await supabase
      .from('collections')
      .delete()
      .eq('id', collectionId);

    if (error) {
      console.error('Error deleting collection:', error);
      return { success: false, error: 'Failed to delete collection' };
    }

    revalidatePath('/dashboard/collections');
    return { success: true };
  } catch (error) {
    console.error('Error in deleteCollection:', error);
    return { success: false, error: 'Something went wrong' };
  }
}
