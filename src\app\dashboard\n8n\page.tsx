'use client';

import { useState, useEffect } from 'react';
import { 
  Title, 
  Text, 
  Card, 
  Group, 
  Stack, 
  Button,
  Badge,
  Code,
  Divider,
  Alert,
  Tabs,
  TextInput,
  Select,
  JsonInput,
  Notification,
  CopyButton,
  ActionIcon,
  Tooltip,
  LoadingOverlay,
} from '@mantine/core';
import { 
  IconRobot, 
  IconCheck, 
  IconX, 
  IconCopy,
  IconExternalLink,
  IconInfoCircle,
  IconTestPipe,
  IconApi,
  IconBook,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

interface Collection {
  id: string;
  name: string;
  description?: string;
}

export default function N8nPage() {
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<string>('');
  const [testQuestion, setTestQuestion] = useState('What is the price?');
  const [testResult, setTestResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [statusData, setStatusData] = useState<any>(null);

  // Load collections on component mount
  useEffect(() => {
    fetchCollections();
    checkN8nStatus();
  }, []);

  const fetchCollections = async () => {
    try {
      const response = await fetch('/api/collections');
      if (response.ok) {
        const data = await response.json();
        setCollections(data.collections || []);
        if (data.collections?.length > 0) {
          setSelectedCollection(data.collections[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to fetch collections:', error);
    }
  };

  const checkN8nStatus = async () => {
    try {
      const response = await fetch('/api/n8n/status', {
        headers: {
          'x-api-key': 'your-secret-api-key' // This would be configured
        }
      });
      if (response.ok) {
        const data = await response.json();
        setStatusData(data);
      }
    } catch (error) {
      console.error('Failed to check n8n status:', error);
    }
  };

  const testN8nEndpoint = async () => {
    if (!selectedCollection) {
      notifications.show({
        title: 'Error',
        message: 'Please select a collection first',
        color: 'red',
        icon: <IconX size="1rem" />
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/n8n/${selectedCollection}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          question: testQuestion,
          language: 'en'
        })
      });

      const result = await response.json();
      setTestResult(result);

      if (result.success) {
        notifications.show({
          title: 'Test Successful',
          message: 'n8n endpoint is working correctly',
          color: 'green',
          icon: <IconCheck size="1rem" />
        });
      } else {
        notifications.show({
          title: 'Test Failed',
          message: result.error || 'Unknown error',
          color: 'red',
          icon: <IconX size="1rem" />
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Test Error',
        message: 'Failed to test n8n endpoint',
        color: 'red',
        icon: <IconX size="1rem" />
      });
    } finally {
      setLoading(false);
    }
  };

  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://your-domain.com';

  return (
    <Stack gap="lg">
      <div>
        <Group gap="sm" mb="xs">
          <IconRobot size="2rem" />
          <Title order={1}>n8n Integration</Title>
        </Group>
        <Text c="dimmed" size="lg">
          Connect your Q&A collections with n8n automation workflows
        </Text>
      </div>

      {/* Status Card */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group justify="space-between" mb="md">
          <Text fw={500} size="lg">Service Status</Text>
          <Badge 
            color={statusData?.status === 'healthy' ? 'green' : statusData?.status === 'warning' ? 'yellow' : 'red'}
            variant="filled"
          >
            {statusData?.status?.toUpperCase() || 'UNKNOWN'}
          </Badge>
        </Group>
        
        {statusData && (
          <Stack gap="xs">
            <Text size="sm">{statusData.message}</Text>
            <Group gap="md">
              <Badge variant="light" color={statusData.services?.qdrant?.status === 'healthy' ? 'green' : 'red'}>
                Qdrant: {statusData.services?.qdrant?.status}
              </Badge>
              <Badge variant="light" color={statusData.services?.gemini?.status === 'healthy' ? 'green' : 'red'}>
                Gemini: {statusData.services?.gemini?.status}
              </Badge>
            </Group>
          </Stack>
        )}
      </Card>

      <Tabs defaultValue="overview">
        <Tabs.List>
          <Tabs.Tab value="overview" leftSection={<IconBook size="0.8rem" />}>
            Overview
          </Tabs.Tab>
          <Tabs.Tab value="endpoints" leftSection={<IconApi size="0.8rem" />}>
            API Endpoints
          </Tabs.Tab>
          <Tabs.Tab value="test" leftSection={<IconTestPipe size="0.8rem" />}>
            Test Integration
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          <Stack gap="md">
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Title order={3} mb="md">What is n8n Integration?</Title>
              <Text size="sm" mb="md">
                n8n is a powerful workflow automation tool that allows you to connect different services and automate tasks. 
                Our n8n integration provides API endpoints that you can use in your n8n workflows to:
              </Text>
              <Stack gap="xs">
                <Text size="sm">• Query your Q&A collections programmatically</Text>
                <Text size="sm">• Get AI-powered responses based on your knowledge base</Text>
                <Text size="sm">• Search for similar questions and answers</Text>
                <Text size="sm">• Integrate with chatbots, customer support systems, and more</Text>
              </Stack>
            </Card>

            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Title order={3} mb="md">Key Features</Title>
              <Stack gap="sm">
                <Group gap="xs">
                  <IconCheck size="1rem" color="green" />
                  <Text size="sm">RESTful API endpoints compatible with n8n</Text>
                </Group>
                <Group gap="xs">
                  <IconCheck size="1rem" color="green" />
                  <Text size="sm">Multilingual support (English, Hindi-English, Nepali-English)</Text>
                </Group>
                <Group gap="xs">
                  <IconCheck size="1rem" color="green" />
                  <Text size="sm">Vector-based semantic search</Text>
                </Group>
                <Group gap="xs">
                  <IconCheck size="1rem" color="green" />
                  <Text size="sm">AI-powered response generation</Text>
                </Group>
                <Group gap="xs">
                  <IconCheck size="1rem" color="green" />
                  <Text size="sm">Collection-specific endpoints for targeted queries</Text>
                </Group>
              </Stack>
            </Card>

            <Alert icon={<IconInfoCircle size="1rem" />} color="blue">
              <Text size="sm">
                <strong>Getting Started:</strong> To use these endpoints in n8n, you'll need to configure the API key 
                and use the HTTP Request node to make calls to the endpoints listed in the API Endpoints tab.
              </Text>
            </Alert>
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value="endpoints" pt="md">
          <Stack gap="md">
            {/* RAG Chat Endpoint */}
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Title order={4}>RAG Chat Endpoint</Title>
                <Badge color="blue">POST</Badge>
              </Group>
              
              <Code block mb="md">
                {baseUrl}/api/n8n/rag-chat
              </Code>
              
              <Text size="sm" mb="md">
                Generate AI responses based on your Q&A collection context.
              </Text>
              
              <Title order={5} mb="xs">Headers:</Title>
              <Code block mb="md">
{`Content-Type: application/json
x-api-key: your-secret-api-key`}
              </Code>
              
              <Title order={5} mb="xs">Request Body:</Title>
              <Code block mb="md">
{`{
  "question": "What is the price?",
  "collection_id": "your-collection-id",
  "user_id": "your-user-id",
  "language": "en"
}`}
              </Code>
              
              <CopyButton value={`${baseUrl}/api/n8n/rag-chat`}>
                {({ copied, copy }) => (
                  <Button 
                    variant="light" 
                    size="xs" 
                    onClick={copy}
                    leftSection={<IconCopy size="0.8rem" />}
                  >
                    {copied ? 'Copied!' : 'Copy URL'}
                  </Button>
                )}
              </CopyButton>
            </Card>

            {/* Collection-Specific Endpoint */}
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Title order={4}>Collection-Specific Endpoint</Title>
                <Badge color="green">POST</Badge>
              </Group>
              
              <Code block mb="md">
                {baseUrl}/api/n8n/[collection_id]
              </Code>
              
              <Text size="sm" mb="md">
                <strong>Coming Soon:</strong> Direct collection access without authentication for public use.
              </Text>
              
              <Alert icon={<IconInfoCircle size="1rem" />} color="orange">
                <Text size="sm">
                  This endpoint is being developed to allow public access to specific collections 
                  without requiring API keys, making it perfect for public chatbots and integrations.
                </Text>
              </Alert>
            </Card>

            {/* Status Endpoint */}
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Title order={4}>Status Check Endpoint</Title>
                <Badge color="cyan">GET</Badge>
              </Group>
              
              <Code block mb="md">
                {baseUrl}/api/n8n/status
              </Code>
              
              <Text size="sm" mb="md">
                Check the health and availability of all services.
              </Text>
              
              <CopyButton value={`${baseUrl}/api/n8n/status`}>
                {({ copied, copy }) => (
                  <Button 
                    variant="light" 
                    size="xs" 
                    onClick={copy}
                    leftSection={<IconCopy size="0.8rem" />}
                  >
                    {copied ? 'Copied!' : 'Copy URL'}
                  </Button>
                )}
              </CopyButton>
            </Card>
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value="test" pt="md">
          <Stack gap="md">
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Title order={3} mb="md">Test n8n Integration</Title>
              
              <Stack gap="md">
                <Select
                  label="Select Collection"
                  placeholder="Choose a collection to test"
                  value={selectedCollection}
                  onChange={(value) => setSelectedCollection(value || '')}
                  data={collections.map(c => ({ value: c.id, label: c.name }))}
                />
                
                <TextInput
                  label="Test Question"
                  placeholder="Enter a question to test"
                  value={testQuestion}
                  onChange={(e) => setTestQuestion(e.target.value)}
                />
                
                <Button 
                  onClick={testN8nEndpoint}
                  loading={loading}
                  leftSection={<IconTestPipe size="1rem" />}
                >
                  Test Endpoint
                </Button>
              </Stack>
            </Card>

            {testResult && (
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Title order={4} mb="md">Test Results</Title>
                <JsonInput
                  value={JSON.stringify(testResult, null, 2)}
                  readOnly
                  autosize
                  minRows={10}
                  maxRows={20}
                />
              </Card>
            )}
          </Stack>
        </Tabs.Panel>
      </Tabs>

      {/* Documentation Links */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Title order={3} mb="md">Additional Resources</Title>
        <Stack gap="sm">
          <Group gap="xs">
            <IconExternalLink size="1rem" />
            <Text size="sm">
              <a href="/api/n8n/docs" target="_blank" rel="noopener">
                Complete API Documentation
              </a>
            </Text>
          </Group>
          <Group gap="xs">
            <IconExternalLink size="1rem" />
            <Text size="sm">
              <a href="https://docs.n8n.io/" target="_blank" rel="noopener">
                n8n Official Documentation
              </a>
            </Text>
          </Group>
          <Group gap="xs">
            <IconExternalLink size="1rem" />
            <Text size="sm">
              <a href="/dashboard/status" target="_blank" rel="noopener">
                System Status Page
              </a>
            </Text>
          </Group>
        </Stack>
      </Card>
    </Stack>
  );
}
