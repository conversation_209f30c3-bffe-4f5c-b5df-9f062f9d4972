'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Container,
  Stack,
  TextInput,
  Button,
  Card,
  Text,
  Group,
  Avatar,
  Badge,
  Loader,
  Alert,
  ActionIcon,
  Divider,
  Box,
  Title,
  Paper,
  Center,
  Select,
  Textarea,
  Tooltip,
  Menu,
  UnstyledButton
} from '@mantine/core';
import {
  IconSend,
  IconRobot,
  IconUser,
  IconAlertCircle,
  IconMessageCircle,
  IconSearch,
  IconLanguage,
  IconSettings,
  IconCheck,
  IconCheckbox,
  IconCopy
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isLoading?: boolean;
}

interface Collection {
  id: string;
  name: string;
  description?: string;
  qa_count: number;
}

export default function ChatPage() {
  const [collectionId, setCollectionId] = useState('');
  const [collection, setCollection] = useState<Collection | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isChatEnabled, setIsChatEnabled] = useState(false);
  const [isLoadingCollection, setIsLoadingCollection] = useState(false);
  const [language, setLanguage] = useState('ne-en');
  const [showQuickActions, setShowQuickActions] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const languages = [
    { value: 'ne-en', label: 'Nepali-English', flag: '🇳🇵' },
    { value: 'hi-en', label: 'Hindi-English', flag: '🇮🇳' },
    { value: 'en', label: 'English', flag: '🇺🇸' }
  ];

  const quickActions = [
    { text: 'What is the price?', icon: '💰' },
    { text: 'Tell me about quality', icon: '⭐' },
    { text: 'Is it available?', icon: '📦' },
    { text: 'What materials are used?', icon: '🧵' }
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadCollection = async () => {
    if (!collectionId.trim()) {
      notifications.show({
        title: 'Error',
        message: 'Please enter a collection ID',
        color: 'red',
      });
      return;
    }

    setIsLoadingCollection(true);
    try {
      const response = await fetch(`/api/n8n/${collectionId}`);
      const data = await response.json();

      if (response.ok && data.success) {
        setCollection(data.collection);
        setIsChatEnabled(true);
        setMessages([{
          id: '1',
          text: `Hello! 👋 I'm here to help you with questions about "${data.collection.name}".

I can answer questions in Nepali-English mix, Hindi-English, or pure English. What would you like to know?

Try asking about:
• Product details
• Pricing information
• Quality and materials
• Availability`,
          isUser: false,
          timestamp: new Date()
        }]);
        
        notifications.show({
          title: 'Connected!',
          message: `Connected to collection: ${data.collection.name}`,
          color: 'green',
        });
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Collection not found',
          color: 'red',
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to load collection',
        color: 'red',
      });
    } finally {
      setIsLoadingCollection(false);
    }
  };

  const sendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputMessage.trim();
    if (!textToSend || !isChatEnabled || isLoading) return;

    setShowQuickActions(false);

    const userMessage: Message = {
      id: Date.now().toString(),
      text: textToSend,
      isUser: true,
      timestamp: new Date()
    };

    const loadingMessage: Message = {
      id: (Date.now() + 1).toString(),
      text: '',
      isUser: false,
      timestamp: new Date(),
      isLoading: true
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch(`/api/n8n/${collectionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: textToSend,
          language: language
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        const botMessage: Message = {
          id: (Date.now() + 2).toString(),
          text: data.answer,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => prev.slice(0, -1).concat([botMessage]));
      } else {
        const errorMessage: Message = {
          id: (Date.now() + 2).toString(),
          text: data.error || 'Sorry, I encountered an error. Please try again.',
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => prev.slice(0, -1).concat([errorMessage]));
      }
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        text: 'Sorry, I encountered a connection error. Please try again.',
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => prev.slice(0, -1).concat([errorMessage]));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (isChatEnabled) {
        sendMessage();
      } else {
        loadCollection();
      }
    }
  };

  return (
    <Container size="md" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Stack style={{ height: '100%' }} gap="md" py="md">
        {/* Header */}
        <Paper shadow="sm" p="md" radius="md" withBorder style={{
          background: 'linear-gradient(135deg, var(--mantine-color-blue-6) 0%, var(--mantine-color-cyan-5) 100%)',
          color: 'white'
        }}>
          <Group justify="space-between" align="center">
            <Group gap="sm">
              <Avatar size="lg" radius="xl" style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}>
                <IconMessageCircle size={28} />
              </Avatar>
              <div>
                <Title order={2} size="h3" c="white">Chat Assistant</Title>
                <Text size="sm" c="rgba(255,255,255,0.8)">
                  {collection ? `Connected to: ${collection.name}` : 'Enter collection ID to start chatting'}
                </Text>
              </div>
            </Group>

            <Group gap="sm">
              {collection && (
                <Badge variant="light" color="white" size="lg" style={{ color: 'var(--mantine-color-blue-6)' }}>
                  {collection.qa_count} Q&As
                </Badge>
              )}

              {isChatEnabled && (
                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <Tooltip label="Language Settings">
                      <ActionIcon variant="subtle" color="white" size="lg">
                        <IconLanguage size="1.2rem" />
                      </ActionIcon>
                    </Tooltip>
                  </Menu.Target>
                  <Menu.Dropdown>
                    <Menu.Label>Response Language</Menu.Label>
                    {languages.map((lang) => (
                      <Menu.Item
                        key={lang.value}
                        leftSection={<Text>{lang.flag}</Text>}
                        rightSection={language === lang.value ? <IconCheck size="1rem" /> : null}
                        onClick={() => setLanguage(lang.value)}
                      >
                        {lang.label}
                      </Menu.Item>
                    ))}
                  </Menu.Dropdown>
                </Menu>
              )}
            </Group>
          </Group>
        </Paper>

        {/* Collection Input */}
        {!isChatEnabled && (
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Stack gap="md">
              <div>
                <Text fw={500} size="lg" mb="xs">Get Started</Text>
                <Text size="sm" c="dimmed">
                  Enter a collection ID to start chatting with the AI assistant about that specific topic.
                </Text>
              </div>
              
              <Group gap="sm">
                <TextInput
                  placeholder="Enter collection ID (e.g., 6699858145)"
                  value={collectionId}
                  onChange={(e) => setCollectionId(e.target.value)}
                  onKeyPress={handleKeyPress}
                  style={{ flex: 1 }}
                  leftSection={<IconSearch size="1rem" />}
                />
                <Button 
                  onClick={loadCollection}
                  loading={isLoadingCollection}
                  leftSection={<IconMessageCircle size="1rem" />}
                >
                  Connect
                </Button>
              </Group>
            </Stack>
          </Card>
        )}

        {/* Chat Messages */}
        {isChatEnabled && (
          <Card 
            shadow="sm" 
            padding={0} 
            radius="md" 
            withBorder 
            style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
          >
            <Box 
              style={{ 
                flex: 1, 
                overflowY: 'auto', 
                padding: '1rem',
                backgroundColor: 'var(--mantine-color-gray-0)'
              }}
            >
              <Stack gap="md">
                {messages.map((message) => (
                  <Group 
                    key={message.id} 
                    gap="sm" 
                    align="flex-start"
                    justify={message.isUser ? 'flex-end' : 'flex-start'}
                  >
                    {!message.isUser && (
                      <Avatar color="blue" radius="xl" size="sm">
                        <IconRobot size="1rem" />
                      </Avatar>
                    )}
                    
                    <Paper
                      shadow="xs"
                      p="sm"
                      radius="lg"
                      style={{
                        maxWidth: '70%',
                        backgroundColor: message.isUser
                          ? 'var(--mantine-color-blue-6)'
                          : 'white',
                        color: message.isUser ? 'white' : 'inherit',
                        marginLeft: message.isUser ? 'auto' : 0,
                        marginRight: message.isUser ? 0 : 'auto',
                        position: 'relative'
                      }}
                      onDoubleClick={() => {
                        if (!message.isLoading) {
                          navigator.clipboard.writeText(message.text);
                          notifications.show({
                            message: 'Message copied to clipboard',
                            color: 'green',
                            autoClose: 2000,
                          });
                        }
                      }}
                    >
                      {message.isLoading ? (
                        <Group gap="xs">
                          <Loader size="xs" color="blue" />
                          <Text size="sm" c="dimmed">Thinking...</Text>
                        </Group>
                      ) : (
                        <>
                          <Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
                            {message.text}
                          </Text>
                          {!message.isUser && (
                            <Tooltip label="Double-click to copy">
                              <ActionIcon
                                size="xs"
                                variant="subtle"
                                color="gray"
                                style={{
                                  position: 'absolute',
                                  top: '4px',
                                  right: '4px',
                                  opacity: 0.6
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigator.clipboard.writeText(message.text);
                                  notifications.show({
                                    message: 'Message copied!',
                                    color: 'green',
                                    autoClose: 2000,
                                  });
                                }}
                              >
                                <IconCopy size="0.7rem" />
                              </ActionIcon>
                            </Tooltip>
                          )}
                        </>
                      )}
                      <Text
                        size="xs"
                        c={message.isUser ? 'rgba(255,255,255,0.7)' : 'dimmed'}
                        mt="xs"
                      >
                        {message.timestamp.toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Text>
                    </Paper>

                    {message.isUser && (
                      <Avatar color="gray" radius="xl" size="sm">
                        <IconUser size="1rem" />
                      </Avatar>
                    )}
                  </Group>
                ))}

                {/* Quick Action Buttons */}
                {showQuickActions && messages.length > 0 && (
                  <Stack gap="xs" mt="md">
                    <Text size="sm" c="dimmed" ta="center">Quick questions:</Text>
                    <Group gap="xs" justify="center">
                      {quickActions.map((action, index) => (
                        <Button
                          key={index}
                          variant="light"
                          size="xs"
                          radius="xl"
                          leftSection={<span>{action.icon}</span>}
                          onClick={() => sendMessage(action.text)}
                          disabled={isLoading}
                        >
                          {action.text}
                        </Button>
                      ))}
                    </Group>
                  </Stack>
                )}

                <div ref={messagesEndRef} />
              </Stack>
            </Box>

            {/* Message Input */}
            <Divider />
            <Box p="md" style={{ backgroundColor: 'var(--mantine-color-gray-0)' }}>
              <Group gap="sm" align="flex-end">
                <Textarea
                  placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                  style={{ flex: 1 }}
                  disabled={isLoading}
                  autosize
                  minRows={1}
                  maxRows={4}
                  radius="xl"
                />
                <ActionIcon
                  size="xl"
                  variant="filled"
                  color="blue"
                  onClick={sendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  loading={isLoading}
                  radius="xl"
                  style={{
                    background: 'linear-gradient(135deg, var(--mantine-color-blue-6) 0%, var(--mantine-color-cyan-5) 100%)',
                    minWidth: '48px',
                    minHeight: '48px'
                  }}
                >
                  <IconSend size="1.2rem" />
                </ActionIcon>
              </Group>
            </Box>
          </Card>
        )}

        {/* Footer */}
        <Paper p="sm" radius="md" style={{ backgroundColor: 'var(--mantine-color-gray-0)' }}>
          <Group justify="center" gap="md">
            <Group gap="xs">
              <IconRobot size="1rem" color="var(--mantine-color-blue-6)" />
              <Text size="xs" c="dimmed">Powered by AI</Text>
            </Group>
            <Divider orientation="vertical" />
            <Group gap="xs">
              <IconLanguage size="1rem" color="var(--mantine-color-green-6)" />
              <Text size="xs" c="dimmed">
                {languages.find(l => l.value === language)?.flag} {languages.find(l => l.value === language)?.label}
              </Text>
            </Group>
            <Divider orientation="vertical" />
            <Text size="xs" c="dimmed">Sales-focused responses</Text>
          </Group>
        </Paper>
      </Stack>
    </Container>
  );
}
