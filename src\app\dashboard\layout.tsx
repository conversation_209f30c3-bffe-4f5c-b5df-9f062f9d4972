import { getRequiredSession } from '@/lib/auth';
import { getAdminStatus } from '@/lib/admin-auth';
import { DashboardShell } from '@/components/dashboard/dashboard-shell';
import { ErrorBoundary } from '@/components/error-boundary';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getRequiredSession();
  const adminStatus = await getAdminStatus();

  return (
    <ErrorBoundary>
      <DashboardShell user={session.user} isAdmin={adminStatus.isAdmin}>
        {children}
      </DashboardShell>
    </ErrorBoundary>
  );
}
