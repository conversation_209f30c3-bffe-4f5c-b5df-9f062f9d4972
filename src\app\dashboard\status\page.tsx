import { getCurrentUser } from '@/lib/auth';
import { getAdminStatus } from '@/lib/admin-auth';
import { AdminGuard } from '@/components/admin/admin-guard';
import {
  Title,
  Text,
  Card,
  Group,
  Stack,
  Badge,
  SimpleGrid,
  Progress,
  Divider,
  Code,
  Alert,
} from '@mantine/core';
import {
  IconDatabase,
  IconBrain,
  IconServer,
  IconCheck,
  IconX,
  IconAlertTriangle,
  IconInfoCircle,
  IconTestPipe,
} from '@tabler/icons-react';
import { supabase } from '@/lib/supabase';
import { APP_CONFIG } from '@/lib/config';

interface StatusCheck {
  name: string;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details?: any;
  icon: React.ComponentType<any>;
}

async function checkSupabaseConnection(): Promise<StatusCheck> {
  try {
    const { data, error } = await supabase
      .from('collections')
      .select('count', { count: 'exact', head: true });

    if (error) {
      return {
        name: 'Supabase Database',
        status: 'error',
        message: `Connection failed: ${error.message}`,
        icon: IconDatabase,
      };
    }

    return {
      name: 'Supabase Database',
      status: 'healthy',
      message: `Connected successfully. Total collections: ${data || 0}`,
      details: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      },
      icon: IconDatabase,
    };
  } catch (error) {
    return {
      name: 'Supabase Database',
      status: 'error',
      message: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      icon: IconDatabase,
    };
  }
}

async function checkQdrantConnection(): Promise<StatusCheck> {
  // Check if environment variables are set
  if (!process.env.QDRANT_URL) {
    return {
      name: 'Qdrant Vector Database',
      status: 'warning',
      message: 'QDRANT_URL environment variable not set',
      details: {
        note: 'Qdrant is optional for basic functionality',
        required_env: ['QDRANT_URL', 'QDRANT_API_KEY (optional)']
      },
      icon: IconBrain,
    };
  }

  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-qdrant`);
    const result = await response.json();

    if (!response.ok || !result.success) {
      return {
        name: 'Qdrant Vector Database',
        status: 'warning',
        message: result.error || 'Connection failed - Vector search disabled',
        details: {
          ...result.details,
          note: 'App will work without vector search functionality'
        },
        icon: IconBrain,
      };
    }

    return {
      name: 'Qdrant Vector Database',
      status: 'healthy',
      message: `Connected successfully. Collections: ${result.collections?.length || 0}`,
      details: {
        url: result.qdrantUrl,
        hasApiKey: result.hasApiKey,
        collections: result.collections,
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: 'Qdrant Vector Database',
      status: 'warning',
      message: `Connection error - Vector search disabled`,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        note: 'App will work without vector search functionality'
      },
      icon: IconBrain,
    };
  }
}

async function checkQdrantSyncStatus(): Promise<StatusCheck> {
  try {
    // Use dynamic port detection
    const baseUrl = process.env.NEXTAUTH_URL || `http://localhost:${process.env.PORT || 3001}`;
    const response = await fetch(`${baseUrl}/api/test-qa-sync`, { method: 'POST' });
    const result = await response.json();

    if (!response.ok || !result.success) {
      return {
        name: 'Qdrant Sync Workflow',
        status: 'error',
        message: result.error || 'Q&A sync test failed',
        details: result.details || result,
        icon: IconBrain,
      };
    }

    return {
      name: 'Qdrant Sync Workflow',
      status: 'healthy',
      message: `Q&A sync working correctly`,
      details: {
        workflow: result.results?.workflow || [],
        vectorDimensions: result.results?.vectorDimensions,
        searchResults: result.results?.searchResults,
        testCompleted: true
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: 'Qdrant Sync Workflow',
      status: 'error',
      message: 'Failed to test Q&A sync workflow',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        note: 'This affects Q&A pair creation and vector search'
      },
      icon: IconBrain,
    };
  }
}

async function checkRAGChatStatus(): Promise<StatusCheck> {
  try {
    // Use dynamic port detection
    const baseUrl = process.env.NEXTAUTH_URL || `http://localhost:${process.env.PORT || 3001}`;
    const response = await fetch(`${baseUrl}/api/test-rag-chat`, { method: 'POST' });
    const result = await response.json();

    if (!response.ok || !result.success) {
      return {
        name: 'RAG Chat Functionality',
        status: 'error',
        message: result.error || 'RAG chat test failed',
        details: result.details || result,
        icon: IconBrain,
      };
    }

    return {
      name: 'RAG Chat Functionality',
      status: 'healthy',
      message: `RAG chat working correctly`,
      details: {
        workflow: result.results?.workflow || [],
        testQAPairs: result.results?.testQAPairs,
        searchResults: result.results?.searchResults,
        ragAnswerGenerated: !!result.results?.ragAnswer,
        testCompleted: true
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: 'RAG Chat Functionality',
      status: 'error',
      message: 'Failed to test RAG chat functionality',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        note: 'This affects AI-powered question answering'
      },
      icon: IconBrain,
    };
  }
}

async function checkGeminiAPIStatus(): Promise<StatusCheck> {
  const chatConfig = APP_CONFIG.api.chat;
  const embeddingConfig = APP_CONFIG.api.embedding;

  if (!process.env.GEMINI_API_KEY) {
    return {
      name: 'Gemini AI API',
      status: 'warning',
      message: 'GEMINI_API_KEY not configured',
      details: {
        note: 'Required for both RAG chat and embedding functionality',
        chat_model: chatConfig.model,
        embedding_model: embeddingConfig.model,
        required_env: ['GEMINI_API_KEY'],
        setup_instructions: 'Get a free API key from https://makersuite.google.com/app/apikey'
      },
      icon: IconBrain,
    };
  }

  // Test the API key by making a simple request
  try {
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: chatConfig.model });

    // Simple test prompt
    const result = await model.generateContent('Hello');
    const response = result.response;

    if (response.text()) {
      return {
        name: 'Gemini AI API',
        status: 'healthy',
        message: 'API key working correctly',
        details: {
          hasApiKey: true,
          chat_model: chatConfig.model,
          embedding_model: embeddingConfig.model,
          note: 'Used for both RAG chat and embedding generation',
          test_successful: true,
          configured_for: ['chat', 'embeddings']
        },
        icon: IconBrain,
      };
    } else {
      return {
        name: 'Gemini AI API',
        status: 'warning',
        message: 'API key configured but response empty',
        details: {
          hasApiKey: true,
          chat_model: chatConfig.model,
          embedding_model: embeddingConfig.model,
          note: 'May have quota or permission issues'
        },
        icon: IconBrain,
      };
    }
  } catch (error) {
    return {
      name: 'Gemini AI API',
      status: 'error',
      message: 'API key test failed',
      details: {
        hasApiKey: true,
        chat_model: chatConfig.model,
        embedding_model: embeddingConfig.model,
        error: error instanceof Error ? error.message : 'Unknown error',
        troubleshooting: 'Check if API key is valid and has sufficient quota'
      },
      icon: IconBrain,
    };
  }
}

async function checkEmbeddingAPIStatus(): Promise<StatusCheck> {
  const embeddingConfig = APP_CONFIG.api.embedding;

  if (!embeddingConfig.apiKey) {
    return {
      name: `${embeddingConfig.provider.charAt(0).toUpperCase() + embeddingConfig.provider.slice(1)} Embedding API`,
      status: 'warning',
      message: `${embeddingConfig.provider.toUpperCase()}_API_KEY not configured`,
      details: {
        provider: embeddingConfig.provider,
        model: embeddingConfig.model,
        features: embeddingConfig.features,
        required_env: [`${embeddingConfig.provider.toUpperCase()}_API_KEY`],
        setup_instructions: embeddingConfig.provider === 'gemini'
          ? 'Get a free API key from https://makersuite.google.com/app/apikey'
          : 'Configure your API key for the selected provider'
      },
      icon: IconBrain,
    };
  }

  try {
    // Use dynamic port detection
    const baseUrl = process.env.NEXTAUTH_URL || `http://localhost:${process.env.PORT || 3001}`;
    const response = await fetch(`${baseUrl}/api/test-embedding-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'Test embedding generation',
        type: 'search_query'
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      // Provide specific error messages based on status codes
      let errorMessage = result.error || 'Unknown API error';
      let statusType: 'warning' | 'error' = 'error';

      if (response.status === 429) {
        errorMessage = `Rate limited - ${embeddingConfig.provider} API usage exceeded`;
        statusType = 'warning';
      } else if (response.status === 401) {
        errorMessage = `Invalid ${embeddingConfig.provider} API key - check your key`;
        statusType = 'error';
      } else if (response.status === 402) {
        errorMessage = `Insufficient credits - upgrade your ${embeddingConfig.provider} plan`;
        statusType = 'error';
      } else if (response.status === 503) {
        errorMessage = `${embeddingConfig.provider} API temporarily unavailable`;
        statusType = 'warning';
      }

      return {
        name: `${embeddingConfig.provider.charAt(0).toUpperCase() + embeddingConfig.provider.slice(1)} Embedding API`,
        status: statusType,
        message: errorMessage,
        details: {
          provider: embeddingConfig.provider,
          model: embeddingConfig.model,
          status_code: response.status,
          error: result.error,
          api_key_configured: true,
          troubleshooting: response.status === 429
            ? 'API usage limits exceeded. Check your quota.'
            : response.status === 401
            ? 'Check if your API key is correct and active.'
            : `Check ${embeddingConfig.provider} status page for service issues.`
        },
        icon: IconBrain,
      };
    }

    return {
      name: `${embeddingConfig.provider.charAt(0).toUpperCase() + embeddingConfig.provider.slice(1)} Embedding API`,
      status: 'healthy',
      message: `API responding correctly. Model: ${result.model}`,
      details: {
        provider: embeddingConfig.provider,
        model: result.model,
        dimensions: result.dimensions,
        response_time_ms: result.response_time_ms,
        features: embeddingConfig.features,
        type: 'Primary embedding service',
        api_key_configured: true
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: `${embeddingConfig.provider.charAt(0).toUpperCase() + embeddingConfig.provider.slice(1)} Embedding API`,
      status: 'error',
      message: 'Connection test failed',
      details: {
        provider: embeddingConfig.provider,
        model: embeddingConfig.model,
        error: error instanceof Error ? error.message : 'Unknown error',
        api_key_configured: true,
        troubleshooting: 'Check network connectivity and API key validity'
      },
      icon: IconBrain,
    };
  }
}



async function getSystemInfo() {
  const qaCount = await supabase
    .from('qa_pairs')
    .select('count', { count: 'exact', head: true });

  const userCount = await supabase
    .from('users')
    .select('count', { count: 'exact', head: true });

  return {
    totalQAPairs: qaCount.count || 0,
    totalUsers: userCount.count || 0,
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
  };
}

export default async function StatusPage() {
  // Ensure user is authenticated
  await getCurrentUser();

  // Check admin status
  const adminStatus = await getAdminStatus();

  // Use the new status API endpoint
  let statusData: any = null;
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/status`);
    if (response.ok) {
      statusData = await response.json();
    }
  } catch (error) {
    console.error('Failed to fetch status data:', error);
  }

  // Fallback to original checks if API fails
  if (!statusData) {
    const [supabaseStatus, qdrantStatus, embeddingStatus, qdrantSyncStatus, ragChatStatus, geminiStatus, systemInfo] = await Promise.all([
      checkSupabaseConnection(),
      checkQdrantConnection(),
      checkEmbeddingAPIStatus(),
      checkQdrantSyncStatus(),
      checkRAGChatStatus(),
      checkGeminiAPIStatus(),
      getSystemInfo(),
    ]);

    const allChecks = [supabaseStatus, qdrantStatus, embeddingStatus, qdrantSyncStatus, ragChatStatus, geminiStatus];
    const healthyCount = allChecks.filter(check => check.status === 'healthy').length;
    const overallHealth = healthyCount === allChecks.length ? 'healthy' :
                         healthyCount > 0 ? 'warning' : 'error';

    // Create fallback status data structure
    statusData = {
      overall_health: overallHealth,
      services: allChecks,
      system_stats: systemInfo,
      test_results: { summary: { total_tests: 0, passed: 0, failed: 0, warnings: 0, success_rate: 0 }, tests: [] }
    };
  }

  const allChecks = statusData.services || [];
  const healthyCount = allChecks.filter((check: any) => check.status === 'healthy').length;
  const overallHealth = statusData.overall_health || 'error';

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return IconCheck;
      case 'warning': return IconAlertTriangle;
      case 'error': return IconX;
      default: return IconInfoCircle;
    }
  };

  return (
    <AdminGuard isAdmin={adminStatus.isAdmin}>
      <Stack gap="lg">
        <div>
          <Title order={1}>System Status</Title>
          <Text c="dimmed" size="lg">
            Real-time status of all system components and connections
          </Text>
        </div>

      {/* Overall Health */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group justify="space-between" mb="md">
          <Group gap="sm">
            <IconServer size="1.5rem" />
            <Title order={3}>Overall System Health</Title>
          </Group>
          <Badge 
            color={getStatusColor(overallHealth)} 
            variant="filled" 
            size="lg"
          >
            {overallHealth.toUpperCase()}
          </Badge>
        </Group>
        
        <Progress 
          value={(healthyCount / allChecks.length) * 100} 
          color={getStatusColor(overallHealth)}
          size="lg"
          mb="sm"
        />
        
        <Text size="sm" c="dimmed">
          {healthyCount} of {allChecks.length} services operational
        </Text>
      </Card>

      {/* Service Status */}
      <SimpleGrid cols={{ base: 1, md: 2, lg: 3 }} spacing="lg">
        {allChecks.map((check: any, index: number) => {
          const StatusIcon = getStatusIcon(check.status);
          const ServiceIcon = check.icon || IconServer;
          
          return (
            <Card key={index} shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Group gap="xs">
                  <ServiceIcon size="1.2rem" />
                  <Text fw={500} size="sm">{check.name}</Text>
                </Group>
                <StatusIcon 
                  size="1rem" 
                  color={`var(--mantine-color-${getStatusColor(check.status)}-6)`}
                />
              </Group>
              
              <Text size="xs" c="dimmed" mb="md">
                {check.message}
              </Text>
              
              <Badge 
                color={getStatusColor(check.status)} 
                variant="light" 
                size="sm"
              >
                {check.status.toUpperCase()}
              </Badge>
              
              {check.details && (
                <div style={{ marginTop: '12px' }}>
                  <Divider size="xs" mb="xs" />
                  <Code block style={{ fontSize: '10px' }}>
                    {JSON.stringify(check.details, null, 2)}
                  </Code>
                </div>
              )}
            </Card>
          );
        })}
      </SimpleGrid>

      {/* System Information */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group gap="sm" mb="md">
          <IconInfoCircle size="1.2rem" />
          <Title order={3}>System Information</Title>
        </Group>
        
        <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
          <div>
            <Text size="sm" fw={500}>Total Q&A Pairs</Text>
            <Text size="xl" fw={700} c="blue">{statusData.system_stats?.total_qa_pairs || 0}</Text>
          </div>
          <div>
            <Text size="sm" fw={500}>Total Users</Text>
            <Text size="xl" fw={700} c="green">{statusData.system_stats?.total_users || 0}</Text>
          </div>
          <div>
            <Text size="sm" fw={500}>Environment</Text>
            <Badge variant="outline">{statusData.system_stats?.environment || 'unknown'}</Badge>
          </div>
          <div>
            <Text size="sm" fw={500}>Last Updated</Text>
            <Text size="xs" c="dimmed">
              {statusData.performance?.timestamp ? new Date(statusData.performance.timestamp).toLocaleString() : 'Unknown'}
            </Text>
          </div>
        </SimpleGrid>
      </Card>

      {/* Test Results Section */}
      {statusData.test_results && statusData.test_results.tests.length > 0 && (
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group gap="sm" mb="md">
            <IconTestPipe size="1.2rem" />
            <Title order={3}>Automated Test Results</Title>
            <Badge
              color={statusData.test_results.summary.success_rate >= 80 ? 'green' :
                     statusData.test_results.summary.success_rate >= 60 ? 'yellow' : 'red'}
              variant="filled"
            >
              {statusData.test_results.summary.success_rate}% Success Rate
            </Badge>
          </Group>

          <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md" mb="md">
            <div>
              <Text size="sm" fw={500}>Total Tests</Text>
              <Text size="xl" fw={700}>{statusData.test_results.summary.total_tests}</Text>
            </div>
            <div>
              <Text size="sm" fw={500}>Passed</Text>
              <Text size="xl" fw={700} c="green">{statusData.test_results.summary.passed}</Text>
            </div>
            <div>
              <Text size="sm" fw={500}>Failed</Text>
              <Text size="xl" fw={700} c="red">{statusData.test_results.summary.failed}</Text>
            </div>
            <div>
              <Text size="sm" fw={500}>Warnings</Text>
              <Text size="xl" fw={700} c="yellow">{statusData.test_results.summary.warnings}</Text>
            </div>
          </SimpleGrid>

          <Divider mb="md" />

          <Stack gap="sm">
            {statusData.test_results.tests.map((test: any, index: number) => (
              <Group key={index} justify="space-between">
                <Group gap="xs">
                  {test.status === 'passed' && <IconCheck size="1rem" color="green" />}
                  {test.status === 'failed' && <IconX size="1rem" color="red" />}
                  {test.status === 'warning' && <IconAlertTriangle size="1rem" color="orange" />}
                  <Text size="sm" fw={500}>{test.test_name}</Text>
                </Group>
                <Group gap="xs">
                  <Text size="xs" c="dimmed">{test.message}</Text>
                  <Badge
                    size="xs"
                    color={test.status === 'passed' ? 'green' : test.status === 'failed' ? 'red' : 'yellow'}
                    variant="light"
                  >
                    {test.status.toUpperCase()}
                  </Badge>
                </Group>
              </Group>
            ))}
          </Stack>
        </Card>
      )}

      <Alert icon={<IconInfoCircle size="1rem" />} color="blue">
        <Text size="sm">
          This page automatically checks the status of all system components and runs automated tests.
          Refresh the page to get the latest status information and test results.
        </Text>
      </Alert>

      {/* Setup Instructions */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Title order={4} mb="md">Setup Instructions</Title>
        <Stack gap="sm">
          <div>
            <Text fw={500} size="sm">For optimal embedding performance (Recommended):</Text>
            <Text size="xs" c="dimmed">
              1. Get an API key from <a href="https://openrouter.ai" target="_blank" rel="noopener">OpenRouter.ai</a><br/>
              2. Add OPENROUTER_API_KEY to your .env.local file<br/>
              3. Provides fast multilingual embeddings with Hindi-English and Nepali-English support
            </Text>
          </div>
          <div>
            <Text fw={500} size="sm">To enable Qdrant vector search:</Text>
            <Text size="xs" c="dimmed">
              1. Set up a Qdrant instance (local or cloud)<br/>
              2. Add QDRANT_URL to your environment variables<br/>
              3. Optionally add QDRANT_API_KEY for authentication
            </Text>
          </div>
          <div>
            <Text fw={500} size="sm">Embedding fallback system:</Text>
            <Text size="xs" c="dimmed">
              OpenRouter API → Browser-based embedding → Mathematical fallback<br/>
              The system automatically falls back if any method fails.
            </Text>
          </div>
          <div>
            <Text fw={500} size="sm">Core functionality:</Text>
            <Text size="xs" c="dimmed">
              The app works fully without any external APIs - fallback systems ensure embedding generation never fails completely.
            </Text>
          </div>
        </Stack>
      </Card>
    </Stack>
    </AdminGuard>
  );
}
