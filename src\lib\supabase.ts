import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Export createClient function for components that need to create their own instance
export { createClient };

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          image?: string;
          created_at: string;
        };
        Insert: {
          id: string;
          email: string;
          name: string;
          image?: string;
        };
      };
      collections: {
        Row: {
          id: string;
          name: string;
          description?: string;
          sales_instructions?: string;
          user_id: string;
          created_at: string;
        };
        Insert: {
          id?: string; // Optional since we'll generate it manually
          name: string;
          description?: string;
          sales_instructions?: string;
          user_id: string;
        };
      };
      qa_pairs: {
        Row: {
          id: string;
          question: string;
          answer: string;
          collection_id: string;
          created_at: string;
        };
        Insert: {
          id?: string; // Optional since we'll generate it manually
          question: string;
          answer: string;
          collection_id: string;
        };
      };
    };
  };
};