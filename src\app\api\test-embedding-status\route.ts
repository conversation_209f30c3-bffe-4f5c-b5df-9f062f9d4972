import { NextRequest, NextResponse } from 'next/server';
import { APP_CONFIG } from '@/lib/config';

async function runEmbeddingStatusTest(query = 'Test embedding generation', type = 'search_query') {
  try {

    // Check if <PERSON> is configured
    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Gemini API key not configured',
          details: 'Please set GEMINI_API_KEY environment variable'
        },
        { status: 503 }
      );
    }

    // Prepare text based on type
    const text = `Query: ${query}`;

    console.log(`🌐 Testing Gemini embedding for status check...`);

    const startTime = Date.now();

    // Call Gemini API for embeddings using centralized config
    const embeddingConfig = APP_CONFIG.api.embedding;
    const response = await fetch(`${embeddingConfig.endpoint}/models/${embeddingConfig.model}:embedContent?key=${embeddingConfig.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: `models/${embeddingConfig.model}`,
        content: {
          parts: [{ text }]
        }
      }),
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Gemini API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      
      return NextResponse.json(
        { 
          success: false,
          error: 'Gemini API request failed',
          details: errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status
        },
        { status: response.status }
      );
    }

    const result = await response.json();
    
    // Extract embedding from Gemini response
    if (!result.embedding || !result.embedding.values) {
      console.error('❌ Invalid Gemini response structure:', result);
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid response from Gemini API',
          details: 'Expected embedding.values in response'
        },
        { status: 500 }
      );
    }

    const embedding = result.embedding.values;

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Generated embedding is empty',
          details: 'Gemini API returned empty embedding vector'
        },
        { status: 500 }
      );
    }
    
    if (embedding.some((val: number) => !isFinite(val))) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Generated embedding contains invalid values',
          details: 'Embedding vector contains NaN or infinite values'
        },
        { status: 500 }
      );
    }

    console.log('✅ Gemini embedding test successful:', {
      model: embeddingConfig.model,
      dimensions: embedding.length,
      response_time_ms: responseTime,
      text_length: text.length
    });

    return NextResponse.json({
      success: true,
      embedding,
      dimensions: embedding.length,
      method: 'gemini-api',
      model: embeddingConfig.model,
      text_length: text.length,
      response_time_ms: responseTime,
      type,
      features: embeddingConfig.features
    });

  } catch (error) {
    console.error('❌ Gemini embedding test failed:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to test Gemini embedding',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint for browser testing
export async function GET() {
  return runEmbeddingStatusTest();
}

// POST endpoint for backward compatibility
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query = 'Test embedding generation', type = 'search_query' } = body;
    return runEmbeddingStatusTest(query, type);
  } catch (error) {
    // If JSON parsing fails, use defaults
    return runEmbeddingStatusTest();
  }
}
