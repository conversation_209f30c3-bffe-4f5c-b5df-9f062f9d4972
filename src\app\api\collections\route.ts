import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch user's collections with Q&A count
    const { data: collections, error } = await supabase
      .from('collections')
      .select(`
        id,
        name,
        description,
        created_at,
        qa_pairs(count)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching collections:', error);
      return NextResponse.json(
        { error: 'Failed to fetch collections' },
        { status: 500 }
      );
    }

    // Transform the data to include Q&A count
    const transformedCollections = (collections || []).map(collection => ({
      id: collection.id,
      name: collection.name,
      description: collection.description,
      created_at: collection.created_at,
      qa_count: collection.qa_pairs?.[0]?.count || 0
    }));

    return NextResponse.json({
      success: true,
      collections: transformedCollections,
      total: transformedCollections.length
    });

  } catch (error) {
    console.error('Error in collections API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
