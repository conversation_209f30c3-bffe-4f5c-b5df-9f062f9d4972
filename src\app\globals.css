@import "tailwindcss";

/* Remove conflicting CSS variables that override <PERSON><PERSON>'s dark mode */
/* Let <PERSON><PERSON> handle the theme completely */

/* Only keep font variables for Tailwind compatibility */
:root {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Remove body background/color overrides to let <PERSON><PERSON> control theming */

/* Fix for large chevron arrow blocking interface - only target accordion chevrons */
.mantine-Accordion-chevron {
  display: none !important;
}

/* Hide any problematic large arrows or chevrons - but not in menus */
[data-chevron="true"]:not(.mantine-Menu-target *) {
  display: none !important;
}

/* Ensure no large pseudo-elements are blocking the interface - but allow normal icons */
body *::before,
body *::after {
  max-width: 100px !important;
  max-height: 100px !important;
}

/* Ensure profile menu chevron is properly sized */
.mantine-Menu-target .tabler-icon {
  width: 14px !important;
  height: 14px !important;
}
