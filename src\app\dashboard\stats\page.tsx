import { getCurrentUser } from '@/lib/auth';
import { getAdminStatus } from '@/lib/admin-auth';
import { AdminGuard } from '@/components/admin/admin-guard';
import {
  Title,
  Text,
  Card,
  Group,
  Stack,
  Badge,
  SimpleGrid,
  Progress,
  Divider,
  Code,
  Alert,
  RingProgress,
  Center,
} from '@mantine/core';
import { 
  IconDatabase, 
  IconBrain,
  IconUsers,
  IconSection,
  IconQuestionMark,
  IconChartBar,
  IconInfoCircle,
  IconServer,
} from '@tabler/icons-react';
import { supabase } from '@/lib/supabase';

interface DatabaseStats {
  totalCollections: number;
  totalQAPairs: number;
  totalUsers: number;
  collectionsPerUser: number;
  qaPairsPerCollection: number;
  recentActivity: {
    collectionsThisWeek: number;
    qaPairsThisWeek: number;
  };
}

interface QdrantStats {
  connected: boolean;
  collections: number;
  totalVectors: number;
  error?: string;
}

async function getDatabaseStats(): Promise<DatabaseStats> {
  try {
    // Get total counts
    const [collectionsResult, qaPairsResult, usersResult] = await Promise.all([
      supabase.from('collections').select('count', { count: 'exact', head: true }),
      supabase.from('qa_pairs').select('count', { count: 'exact', head: true }),
      supabase.from('users').select('count', { count: 'exact', head: true }),
    ]);

    const totalCollections = collectionsResult.count || 0;
    const totalQAPairs = qaPairsResult.count || 0;
    const totalUsers = usersResult.count || 0;

    // Get recent activity (last 7 days)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const [recentCollections, recentQAPairs] = await Promise.all([
      supabase
        .from('collections')
        .select('count', { count: 'exact', head: true })
        .gte('created_at', oneWeekAgo.toISOString()),
      supabase
        .from('qa_pairs')
        .select('count', { count: 'exact', head: true })
        .gte('created_at', oneWeekAgo.toISOString()),
    ]);

    return {
      totalCollections,
      totalQAPairs,
      totalUsers,
      collectionsPerUser: totalUsers > 0 ? totalCollections / totalUsers : 0,
      qaPairsPerCollection: totalCollections > 0 ? totalQAPairs / totalCollections : 0,
      recentActivity: {
        collectionsThisWeek: recentCollections.count || 0,
        qaPairsThisWeek: recentQAPairs.count || 0,
      },
    };
  } catch (error) {
    console.error('Error fetching database stats:', error);
    return {
      totalCollections: 0,
      totalQAPairs: 0,
      totalUsers: 0,
      collectionsPerUser: 0,
      qaPairsPerCollection: 0,
      recentActivity: {
        collectionsThisWeek: 0,
        qaPairsThisWeek: 0,
      },
    };
  }
}

async function getQdrantStats(): Promise<QdrantStats> {
  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-qdrant`);
    const result = await response.json();

    if (result.success) {
      const totalVectors = result.collections?.reduce(
        (sum: number, col: any) => sum + (col.vectors_count || 0), 
        0
      ) || 0;

      return {
        connected: true,
        collections: result.collections?.length || 0,
        totalVectors,
      };
    } else {
      return {
        connected: false,
        collections: 0,
        totalVectors: 0,
        error: result.error,
      };
    }
  } catch (error) {
    return {
      connected: false,
      collections: 0,
      totalVectors: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export default async function StatsPage() {
  // Ensure user is authenticated
  await getCurrentUser();

  // Check admin status
  const adminStatus = await getAdminStatus();

  const [dbStats, qdrantStats] = await Promise.all([
    getDatabaseStats(),
    getQdrantStats(),
  ]);

  const activityPercentage = dbStats.totalCollections > 0 
    ? (dbStats.recentActivity.collectionsThisWeek / dbStats.totalCollections) * 100 
    : 0;

  return (
    <AdminGuard isAdmin={adminStatus.isAdmin}>
      <Stack gap="lg">
        <div>
          <Title order={1}>Database Statistics</Title>
          <Text c="dimmed" size="lg">
            Comprehensive overview of your database metrics and usage
          </Text>
        </div>

      {/* Overview Cards */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="lg">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <IconUsers size="1.5rem" color="var(--mantine-color-blue-6)" />
            <Badge variant="light" color="blue">Users</Badge>
          </Group>
          <Text size="2xl" fw={700} c="blue">
            {dbStats.totalUsers.toLocaleString()}
          </Text>
          <Text size="sm" c="dimmed">Total registered users</Text>
        </Card>

        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <IconSection size="1.5rem" color="var(--mantine-color-green-6)" />
            <Badge variant="light" color="green">Collections</Badge>
          </Group>
          <Text size="2xl" fw={700} c="green">
            {dbStats.totalCollections.toLocaleString()}
          </Text>
          <Text size="sm" c="dimmed">Total collections</Text>
        </Card>

        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <IconQuestionMark size="1.5rem" color="var(--mantine-color-orange-6)" />
            <Badge variant="light" color="orange">Q&A Pairs</Badge>
          </Group>
          <Text size="2xl" fw={700} c="orange">
            {dbStats.totalQAPairs.toLocaleString()}
          </Text>
          <Text size="sm" c="dimmed">Total Q&A pairs</Text>
        </Card>

        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <IconBrain size="1.5rem" color="var(--mantine-color-purple-6)" />
            <Badge variant="light" color="purple">Vectors</Badge>
          </Group>
          <Text size="2xl" fw={700} c="purple">
            {qdrantStats.connected ? qdrantStats.totalVectors.toLocaleString() : 'N/A'}
          </Text>
          <Text size="sm" c="dimmed">
            {qdrantStats.connected ? 'Total vectors in Qdrant' : 'Qdrant not connected'}
          </Text>
        </Card>
      </SimpleGrid>

      {/* Detailed Analytics */}
      <SimpleGrid cols={{ base: 1, lg: 2 }} spacing="lg">
        {/* Usage Analytics */}
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group gap="sm" mb="md">
            <IconChartBar size="1.2rem" />
            <Title order={3}>Usage Analytics</Title>
          </Group>
          
          <Stack gap="md">
            <div>
              <Group justify="space-between" mb="xs">
                <Text size="sm" fw={500}>Collections per User</Text>
                <Text size="sm" c="dimmed">{dbStats.collectionsPerUser.toFixed(1)}</Text>
              </Group>
              <Progress value={Math.min(dbStats.collectionsPerUser * 20, 100)} color="blue" size="sm" />
            </div>
            
            <div>
              <Group justify="space-between" mb="xs">
                <Text size="sm" fw={500}>Q&As per Collection</Text>
                <Text size="sm" c="dimmed">{dbStats.qaPairsPerCollection.toFixed(1)}</Text>
              </Group>
              <Progress value={Math.min(dbStats.qaPairsPerCollection * 5, 100)} color="green" size="sm" />
            </div>
            
            <Divider />
            
            <div>
              <Text size="sm" fw={500} mb="xs">Recent Activity (Last 7 Days)</Text>
              <Group gap="md">
                <div>
                  <Text size="xs" c="dimmed">New Collections</Text>
                  <Text fw={600}>{dbStats.recentActivity.collectionsThisWeek}</Text>
                </div>
                <div>
                  <Text size="xs" c="dimmed">New Q&A Pairs</Text>
                  <Text fw={600}>{dbStats.recentActivity.qaPairsThisWeek}</Text>
                </div>
              </Group>
            </div>
          </Stack>
        </Card>

        {/* Vector Database Status */}
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group gap="sm" mb="md">
            <IconServer size="1.2rem" />
            <Title order={3}>Vector Database</Title>
          </Group>
          
          {qdrantStats.connected ? (
            <Stack gap="md">
              <Center>
                <RingProgress
                  size={120}
                  thickness={12}
                  sections={[
                    { value: 100, color: 'green' },
                  ]}
                  label={
                    <Center>
                      <IconBrain size="1.5rem" color="var(--mantine-color-green-6)" />
                    </Center>
                  }
                />
              </Center>
              
              <div style={{ textAlign: 'center' }}>
                <Text fw={500} c="green">Connected</Text>
                <Text size="sm" c="dimmed">Qdrant is operational</Text>
              </div>
              
              <SimpleGrid cols={2} spacing="sm">
                <div style={{ textAlign: 'center' }}>
                  <Text size="xl" fw={700}>{qdrantStats.collections}</Text>
                  <Text size="xs" c="dimmed">Collections</Text>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <Text size="xl" fw={700}>{qdrantStats.totalVectors.toLocaleString()}</Text>
                  <Text size="xs" c="dimmed">Vectors</Text>
                </div>
              </SimpleGrid>
            </Stack>
          ) : (
            <Stack gap="md">
              <Center>
                <RingProgress
                  size={120}
                  thickness={12}
                  sections={[
                    { value: 100, color: 'red' },
                  ]}
                  label={
                    <Center>
                      <IconDatabase size="1.5rem" color="var(--mantine-color-red-6)" />
                    </Center>
                  }
                />
              </Center>
              
              <div style={{ textAlign: 'center' }}>
                <Text fw={500} c="red">Disconnected</Text>
                <Text size="sm" c="dimmed">Vector search unavailable</Text>
              </div>
              
              {qdrantStats.error && (
                <Alert color="red" size="sm">
                  <Text size="xs">{qdrantStats.error}</Text>
                </Alert>
              )}
            </Stack>
          )}
        </Card>
      </SimpleGrid>

      {/* System Information */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group gap="sm" mb="md">
          <IconInfoCircle size="1.2rem" />
          <Title order={3}>System Information</Title>
        </Group>
        
        <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
          <div>
            <Text size="sm" fw={500}>Environment</Text>
            <Badge variant="outline">{process.env.NODE_ENV}</Badge>
          </div>
          <div>
            <Text size="sm" fw={500}>Database</Text>
            <Text size="sm" c="dimmed">Supabase PostgreSQL</Text>
          </div>
          <div>
            <Text size="sm" fw={500}>Vector Search</Text>
            <Text size="sm" c="dimmed">
              {qdrantStats.connected ? 'Qdrant (Active)' : 'Disabled'}
            </Text>
          </div>
        </SimpleGrid>
        
        <Divider my="md" />
        
        <Text size="xs" c="dimmed">
          Last updated: {new Date().toLocaleString()}
        </Text>
      </Card>
    </Stack>
    </AdminGuard>
  );
}
