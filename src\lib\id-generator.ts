/**
 * Generate a simple random numeric ID
 * Returns a 10-digit number as a string for better readability
 */
export function generateSimpleId(): string {
  // Generate a random number between 1000000000 and 9999999999 (10 digits)
  const randomId = Math.floor(Math.random() * 9000000000) + 1000000000;
  return randomId.toString();
}

/**
 * Generate a simple random numeric ID for QA pairs
 * Returns a 10-digit number as a string
 */
export function generateQAPairId(): string {
  return generateSimpleId();
}

/**
 * Generate a simple random numeric ID for collections
 * Returns a 10-digit number as a string
 */
export function generateCollectionId(): string {
  return generateSimpleId();
}
